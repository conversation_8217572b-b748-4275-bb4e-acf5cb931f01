<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px;">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeltInWin</title>
    <!-- Foontawsome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
    <style id="_goober">
        @keyframes go2264125279 {
            from {
                transform: scale(0) rotate(45deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(45deg);
                opacity: 1;
            }
        }
        
        @keyframes go3020080000 {
            from {
                transform: scale(0);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        @keyframes go463499852 {
            from {
                transform: scale(0) rotate(90deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(90deg);
                opacity: 1;
            }
        }
        
        @keyframes go1268368563 {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }
        
        @keyframes go1310225428 {
            from {
                transform: scale(0) rotate(45deg);
                opacity: 0;
            }
            to {
                transform: scale(1) rotate(45deg);
                opacity: 1;
            }
        }
        
        @keyframes go651618207 {
            0% {
                height: 0;
                width: 0;
                opacity: 0;
            }
            40% {
                height: 0;
                width: 6px;
                opacity: 1;
            }
            100% {
                opacity: 1;
                height: 10px;
            }
        }
        
        @keyframes go901347462 {
            from {
                transform: scale(0.6);
                opacity: 0.4;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }
        
        .go4109123758 {
            z-index: 9999;
        }
        
        .go4109123758>* {
            pointer-events: auto;
        }
    </style>
</head>

<body style="font-size: 12px; background-color: #f8f9fa;">

    <body cz-shortcut-listen="true">
        <div id="root">
            <div class="">
                <a href="/customerSupport">
                    <div class="iconrob" style="position: fixed; right: 0px; bottom: 0px; width: 40px; height: 40px;"><img src="/assets/icon-BMOz9JeZ.png" alt="Draggable Icon" style="position: absolute; left: -50px; top: -50px; cursor: move;"></div>
                </a>
            </div>
            <div class="mainApp">
                <div style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"></div>
                <div class="container-fluid subordinate headertop bg-white rounded-0 py-2">
                    <div class="row align-items-center">
                        <div class="col-auto text-center"><a class="btn hedbtn text-end shadow-none me-auto" href="/accounts"><span><i class="fa-solid fa-angle-left text-center text-black"></i></span></a></div>
                        <div class="col">
                            <div class="headertext text-center fw-bold">Withdraw History</div>
                        </div>
                    </div>
                </div>
                <div class="container-fluid lotterysectionsection1 mb-3">
                    <div class="row">
                        <div class="col-12 px-1 px-2">
                            <div class="card withdrwalcard border-0 mb-3">
                                <div class="card-body p-1 p-sm-2">
                                    <div class="buttonfiledsection pb-2 mb-3"><button class="btn depositbtn">Withdraw</button></div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Amount (INR)</div>
                                        <div class="subheading text-success fw-bold">₹ 900</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Amount (USDT)</div>
                                        <div class="subheading text-success fw-bold">$ 0</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Txn Hash</div>
                                        <div class="subheading text-black"><a href="https://bscscan.com/tx/" target="_blank" style="text-decoration: none;">View Hash</a></div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Date</div>
                                        <div class="subheading text-black">23/04/2025, 04:56 PM</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Status</div>
                                        <div class="subheading text-warning">Cancel</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Paid Date</div>
                                        <div class="subheading text-black">23/04/2025, 07:25 PM</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Remark</div>
                                        <div class="subheading copytext text-black">Manually Cancel</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 px-1 px-2">
                            <div class="card withdrwalcard border-0 mb-3">
                                <div class="card-body p-1 p-sm-2">
                                    <div class="buttonfiledsection pb-2 mb-3"><button class="btn depositbtn">Withdraw</button></div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Amount (INR)</div>
                                        <div class="subheading text-success fw-bold">₹ 900</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Amount (USDT)</div>
                                        <div class="subheading text-success fw-bold">$ 0</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Txn Hash</div>
                                        <div class="subheading text-black"><a href="https://bscscan.com/tx/" target="_blank" style="text-decoration: none;">View Hash</a></div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Requested Date</div>
                                        <div class="subheading text-black">23/04/2025, 04:29 PM</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Status</div>
                                        <div class="subheading text-warning">Cancel</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Paid Date</div>
                                        <div class="subheading text-black">23/04/2025, 04:46 PM</div>
                                    </div>
                                    <div class="text-section mb-3">
                                        <div class="heading">Remark</div>
                                        <div class="subheading copytext text-black">Manually Cancel</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>


    </body>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        // Global variables
        let allWithdrawals = [];
        let currentFilter = 'all';

        $(document).ready(function() {
            loadWithdrawalHistory();
            setupEventListeners();
        });

        function setupEventListeners() {
            // Filter tab clicks
            $('.filter-tab').click(function() {
                $('.filter-tab').removeClass('active');
                $(this).addClass('active');
                currentFilter = $(this).data('status');
                filterWithdrawals();
            });
        }

        function loadWithdrawalHistory() {
            $('#loading-state').show();
            $('#empty-state').hide();
            $('#withdrawal-list').show();

            $.ajax({
                type: "GET",
                url: "/api/webapi/withdraw/list",
                dataType: "json",
                success: function(response) {
                    console.log('Withdrawal history response:', response);

                    if (response.datas && response.datas.length > 0) {
                        allWithdrawals = response.datas;
                        updateSummaryCards();
                        renderWithdrawals(allWithdrawals);
                    } else {
                        showEmptyState();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading withdrawal history:', error);
                    showError('Failed to load withdrawal history');
                },
                complete: function() {
                    $('#loading-state').hide();
                }
            });
        }

        function updateSummaryCards() {
            let totalAmount = 0;
            let pendingAmount = 0;
            let successAmount = 0;

            allWithdrawals.forEach(withdrawal => {
                const amount = parseFloat(withdrawal.money) || 0;
                totalAmount += amount;

                if (withdrawal.status == 1) {
                    successAmount += amount;
                } else if (withdrawal.status == 0) {
                    pendingAmount += amount;
                }
            });

            $('#total-withdrawals').text(`₹${totalAmount.toFixed(2)}`);
            $('#pending-withdrawals').text(`₹${pendingAmount.toFixed(2)}`);
            $('#success-withdrawals').text(`₹${successAmount.toFixed(2)}`);
        }

        function filterWithdrawals() {
            let filteredData = allWithdrawals;

            if (currentFilter !== 'all') {
                filteredData = allWithdrawals.filter(withdrawal =>
                    withdrawal.status == currentFilter
                );
            }

            renderWithdrawals(filteredData);
        }

        function renderWithdrawals(withdrawals) {
            const withdrawalList = $('#withdrawal-list');

            if (withdrawals.length === 0) {
                showEmptyState();
                return;
            }

            let html = '';
            withdrawals.forEach((withdrawal, index) => {
                        const amount = parseFloat(withdrawal.money) || 0;
                        const date = formatDate(withdrawal.time);
                        const status = getStatusInfo(withdrawal.status);
                        const withdrawMethod = withdrawal.withdrawMethod || 'bank';
                        const txnHash = withdrawal.txn_hash || withdrawal.id_order || 'N/A';

                        html += `
                    <div class="card mb-3 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fa-solid ${withdrawMethod === 'usdt' ? 'fa-coins' : 'fa-credit-card'} fa-2x text-primary"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0 fw-bold">${withdrawMethod === 'usdt' ? 'USDT' : 'Bank Card'} Withdrawal</h6>
                                        <small class="text-muted">ID: ${withdrawal.id_order || withdrawal.id || index + 1}</small>
                                    </div>
                                </div>
                                <span class="status-badge ${status.class}">${status.text}</span>
                            </div>

                            <div class="row align-items-center">
                                <div class="col-6">
                                    <h4 class="mb-0 text-success fw-bold">₹${amount.toFixed(2)}</h4>
                                    <small class="text-muted">${date}</small>
                                </div>
                                <div class="col-6 text-end">
                                    ${txnHash !== 'N/A' ? `
                                        <button class="copy-btn btn btn-sm" onclick="copyToClipboard('${txnHash}')">
                                            <i class="fa-solid fa-copy me-1"></i>
                                            Copy Hash
                                        </button>
                                        <div class="mt-1">
                                            <small class="text-muted">${txnHash.substring(0, 8)}...${txnHash.substring(txnHash.length - 8)}</small>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            withdrawalList.html(html);
            $('#empty-state').hide();
        }

        function getStatusInfo(status) {
            switch(parseInt(status)) {
                case 1:
                    return { class: 'status-success', text: 'Success' };
                case 2:
                    return { class: 'status-cancelled', text: 'Cancelled' };
                default:
                    return { class: 'status-pending', text: 'Pending' };
            }
        }

        function formatDate(timestamp) {
            if (!timestamp) return 'N/A';

            const date = new Date(Number(timestamp));
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showAlert('Transaction hash copied to clipboard!', 'success');
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                showAlert('Failed to copy to clipboard', 'error');
            });
        }

        function showEmptyState() {
            $('#withdrawal-list').hide();
            $('#empty-state').show();
        }

        function showError(message) {
            showAlert(message, 'error');
            showEmptyState();
        }

        function showAlert(message, type = 'info') {
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' : 'alert-info';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show position-fixed"
                     style="top: 20px; right: 20px; z-index: 9999; min-width: 300px;">
                    <i class="fa-solid ${type === 'success' ? 'fa-check-circle' :
                                        type === 'error' ? 'fa-exclamation-triangle' : 'fa-info-circle'}"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            $('body').append(alertHtml);

            // Auto remove after 5 seconds
            setTimeout(() => {
                $('.alert').fadeOut(500, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    </script>
</body>

</html>