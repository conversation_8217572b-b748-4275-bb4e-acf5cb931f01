<!DOCTYPE html>
<html lang="en" translate="no" data-dpr="1" style="font-size: 40.5px;">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Home</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link rel="stylesheet" href="./index_files/modules-96c1e775.css">
    <link rel="stylesheet" href="./index_files/page-activity-d48e62db.css">
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link rel="stylesheet" href="./index_files/page-home-58543d87.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <style>
        * {
            box-sizing: border-box;
        }
        
        body {
            font-family: Verdana, sans-serif;
        }
        
        .mySlides {
            display: none;
        }
        
        img {
            vertical-align: middle;
        }
        
        .van-dialog1 {
            border-radius: 10px;
            background: #292929;
            color: #fff;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            position: fixed;
            max-width: 400px;
            margin: auto;
            margin-left: 16px;
            margin-right: 16px;
            margin-top: 50%;
        }
        
        .van-popup2 {
            position: fixed;
            max-width: 80%;
            overflow-y: auto;
            margin-left: px;
            box-sizing: border-box;
            background: var(--van-popup-background);
            -webkit-transition: var(--van-popup-transition);
            transition: var(--van-popup-transition);
            -webkit-overflow-scrolling: touch;
            /* margin-right: 10%; */
        }
        
        .van-dialog2 {
            border-radius: 10px;
            background: #292929;
            color: #fff;
            padding: 20px;
            font-family: 'Arial', sans-serif;
            position: fixed;
        }
        
        .promptHeader {
            background: #FFD700;
            /* Gold background */
            text-align: center;
            color: #000;
            font-weight: bold;
            padding: 1px 0;
            border-top-left-radius: 0px;
            border-top-right-radius: 0px;
            position: relative;
            margin-bottom: 1px;
        }
        
        .promptContent {
            padding: 0 20px;
        }
        
        .van-dialog__footer {
            text-align: center;
            padding-top: 0px;
        }
        
        .van-button__text {
            background: linear-gradient(to bottom, #FFD700, #ff9800);
            /* Gold background */
            color: #000;
            padding: 10px 30px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            display: inline-block;
            margin-top: 0px;
            margin-left: 25%;
        }
        /* Custom icons */
        
        .icon {
            margin-right: 5px;
            color: #FFD700;
        }
        /* Fire icon color */
        
        .fire-icon {
            color: #FF4500;
        }
        /* Gem icon color */
        
        .gem-icon {
            color: #00BFFF;
        }
        
        .promptHeadear {
            background: linear-gradient(to bottom, #FFf, #999898);
            /* Gold background */
            text-align: center;
            color: #000;
            font-weight: bold;
            padding: 10px 0;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            border-bottom-left-radius: 10px;
            border-bottom-right-radius: 10px;
            position: relative;
            margin-bottom: 20px;
        }
        /* Adjustments for icons and text alignment */
        
        .list-unstyled li {
            list-style-type: none;
            padding: 5px 0;
        }
        
        .list-unstyled li:before {
            content: '★ ';
            color: #FFD700;
        }
        /* Specific icons for some list items */
        
        .list-unstyled .fire:before {
            content: '🔥 ';
        }
        
        .list-unstyled .gem:before {
            content: '💎 ';
        }
        
        .list-unstyled .gift:before {
            content: '🎁 ';
        }
        /* Slideshow container */
        
        .slideshow-container {
            max-width: 1000px;
            position: relative;
            margin: auto;
        }
        /* Caption text */
        
        .text {
            color: #f2f2f2;
            font-size: 15px;
            padding: 8px 12px;
            position: absolute;
            bottom: 8px;
            width: 100%;
            text-align: center;
        }
        /* Number text (1/3 etc) */
        
        .numbertext {
            color: #f2f2f2;
            font-size: 12px;
            padding: 8px 12px;
            position: absolute;
            top: 0;
        }
        /* The dots/bullets/indicators */
        
        .dot {
            height: 0px;
            width: 0px;
            margin: 0 2px;
            background-color: #bbb;
            border-radius: 50%;
            display: inline-block;
            transition: background-color 0.6s ease;
        }
        
        .active {
            background-color: #717171;
        }
        /* Fading animation */
        
        .fade {
            animation-name: fade;
            animation-duration: 0.3s;
        }
        
        @keyframes fade {
            from {
                opacity: .4
            }
            to {
                opacity: 1
            }
        }
        /* On smaller screens, decrease text size */
        
        @media only screen and (max-width: 300px) {
            .text {
                font-size: 11px
            }
        }
        
        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ccc;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            width: 37%;
            padding-top: 10px;
        }
        
        .popup-container {
            position: relative;
        }
        
        .popup-container .button {
            position: absolute;
            right: 0;
            border: none;
            font-size: 14px;
            top: -9px;
        }
        
        .balance-section {
            padding: .13333rem .26667rem;
            border-radius: .13333rem;
            height: .8rem;
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center;
            font-size: .32rem;
            color: #292929;
            background: -webkit-linear-gradient(top, #F6E3A3 0%, #D2A753 100%);
            background: linear-gradient(180deg, #F6E3A3 0%, #D2A753 100%);
            width: 96%;
            margin: auto;
            margin-top: 15px;
        }
    </style>
    <style>
        .material-symbols-outlined {
            font-variation-settings: 'FILL' 0, 'wght' 400, 'GRAD' 0, 'opsz' 24
        }
    </style>
</head>
<!-- Dialog HTML -->
<div role="dialog" class="van-popup van-dialog1" style="top:50px; z-index: 2003; display: none;">
    <div class="promptHeader">Notification</div>
    <div class="promptContent">
        <ul class="list-unstyled" style="text-align: center;">
            <li>91trade Game Operating 5 Years+</li>
            <li>The Most Professional Game</li>
            <li>High-Quality Agent Benefits</li>
            <li>No.1 Casino Game Platform</li>
            <li class="fire">Local Bank Deposit 2% Bonus</li>
            <li class="fire">USDT 3% Bonus &TRX 2% Bonus</li>
            <li class="gift">More Bonus - Click EVENT</li>
            <li class="gem">Click Promote - Become Agent</li>
            <li>Get income every day</li>
        </ul>
    </div>
    <div class="van-dialog__footer">
        <span class="van-button__text">Confirm</span>
    </div>
</div>
<div class="van-overlay" style="z-index: 2003; display: none;">
    <div data-v-7692a079="" data-v-42f27458="" class="Loading c-row c-row-middle-center" style="position: fixed;height: 100vh;width: 100vw;top: 0;left: 0;background: rgba(0,0,0,.6);z-index: 99999;">
        <div data-v-7692a079="" class="van-loading van-loading--circular">
            <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular" style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet" style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                        <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)" style="display: block;">
                            <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice" xlink:href="/index_files/loadingspinner.png"></image>
                        </g>
                    </svg>
                </span>
            <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
        </div>
    </div>
</div>

<body style="font-size: 12px;">
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="position: absolute; width: 0; height: 0">
            <symbol id="icon-Line" viewBox="0 0 2 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1 1L0.999999 21" stroke="#888888" stroke-linecap="round"></path>
            </symbol>
            <symbol id="icon-SABA" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36307)">
                    <g filter="url(#filter0_d_1701_36307)">
                        <path d="M17.6816 24.4392H15.8146V26.1964H17.6816C17.915 26.1737 18.1403 26.0986 18.3406 25.9768C18.5603 25.7571 18.5603 25.6473 18.5603 25.3178C18.5375 25.0844 18.4624 24.8592 18.3406 24.6588C18.2538 24.5758 18.1497 24.5131 18.0358 24.4751C17.9218 24.4371 17.8009 24.4248 17.6816 24.4392Z" fill="white"></path>
                        <path d="M5.16186 31.1387C5.71637 32.345 6.33974 33.5184 7.02891 34.6532C7.13873 34.8728 7.24856 34.9827 7.35839 35.2023C7.00105 34.2715 6.7075 33.3175 6.47978 32.3468C6.1503 32.0173 5.60116 31.578 5.16186 31.1387Z" fill="white"></path>
                        <path d="M6.36995 31.3584C5.93169 29.4491 5.71063 27.4965 5.71099 25.5376C5.71099 24.4393 5.82082 23.341 5.93064 22.2428C4.62388 20.2282 3.58983 18.0493 2.8555 15.763C2.32927 20.4859 2.93151 25.2662 4.61273 29.711C5.16186 30.2601 5.71099 30.8092 6.36995 31.3584Z" fill="white"></path>
                        <path d="M6.36995 23.1214C6.27273 23.9597 6.23603 24.8038 6.26012 25.6474C6.29049 27.8282 6.54814 29.9998 7.02891 32.1272C10.3159 34.8226 14.2558 36.6031 18.4509 37.289C19.574 37.4976 20.7132 37.6078 21.8555 37.6185C24.0893 37.0351 26.2326 36.1482 28.2254 34.9827C23.914 34.8271 19.6972 33.6753 15.9054 31.6175C12.1136 29.5596 8.84986 26.6516 6.36995 23.1214Z" fill="white"></path>
                        <path d="M7.35839 33.0058C7.71804 34.2919 8.196 35.5419 8.78613 36.7399C11.0445 37.5646 13.4139 38.0459 15.815 38.1676C17.1003 38.2098 18.3868 38.1363 19.659 37.948C19.2489 37.9238 18.8432 37.8501 18.4509 37.7283C14.4247 37.0784 10.6174 35.4575 7.35839 33.0058Z" fill="white"></path>
                        <path d="M19 5C15.2422 5 11.5687 6.11433 8.44417 8.20208C5.31964 10.2898 2.88436 13.2572 1.4463 16.729C0.00823312 20.2008 -0.368031 24.0211 0.365088 27.7067C1.09821 31.3923 2.90778 34.7778 5.56498 37.435C8.22218 40.0922 11.6076 41.9018 15.2933 42.6349C18.9789 43.368 22.7992 42.9918 26.271 41.5537C29.7428 40.1156 32.7102 37.6804 34.7979 34.5558C36.8857 31.4313 38 27.7578 38 24C37.9971 18.9618 35.9944 14.1307 32.4318 10.5682C28.8693 7.00562 24.0382 5.00291 19 5ZM32.948 28.3931H30.6416L28.0058 23.5607L22.5145 25.5376L26.9075 17.5202L32.948 28.3931ZM26.2486 9.72254L31.4104 19.1676H29.1041L26.7977 14.9942L22.6243 16.3121L26.2486 9.72254ZM21.7457 17.7399L24.6012 16.8613L23.3931 19.1676H21.0867L21.7457 17.7399ZM21.6358 26.9653L24.4913 26.0867L23.2832 28.3931H20.9769L21.6358 26.9653ZM20.6474 21.5838C20.6654 22.2995 20.4323 22.9989 19.9884 23.5607C20.2323 23.8664 20.4136 24.2171 20.5219 24.5929C20.6302 24.9687 20.6633 25.3621 20.6195 25.7508C20.5756 26.1394 20.4556 26.5155 20.2663 26.8577C20.077 27.1999 19.8221 27.5014 19.5162 27.7451C18.9968 28.1642 18.3495 28.3929 17.6821 28.3931H13.6185V20.9249H15.7052V22.4624H17.6821C17.9155 22.4397 18.1407 22.3646 18.341 22.2428C18.5607 22.0231 18.5607 21.9133 18.5607 21.5838C18.5607 21.3462 18.4836 21.115 18.341 20.9249C19.0453 20.7203 19.6872 20.3428 20.2081 19.8266C20.4845 20.3719 20.6347 20.9725 20.6474 21.5838ZM19.4393 9.61272V11.8092H16.0347C15.8013 11.832 15.5761 11.9071 15.3757 12.0289C15.1561 12.2486 15.1561 12.3584 15.1561 12.6879C15.1788 12.9212 15.2539 13.1465 15.3757 13.3468C15.5954 13.5665 15.7052 13.5665 16.0347 13.5665H17.133C17.9487 13.5773 18.7313 13.8904 19.3295 14.4451C19.6137 14.7351 19.8377 15.0785 19.9885 15.4555C20.1393 15.8325 20.2139 16.2356 20.2081 16.6416C20.1972 17.4573 19.8842 18.24 19.3295 18.8381C19.0395 19.1224 18.6961 19.3463 18.3191 19.4971C17.9421 19.6479 17.539 19.7226 17.133 19.7168H13.7283V17.5202H17.133C17.3663 17.4975 17.5916 17.4224 17.7919 17.3006C18.0116 17.0809 18.0116 16.9711 18.0116 16.6416C17.9888 16.4082 17.9137 16.183 17.7919 15.9827C17.7051 15.8996 17.601 15.8369 17.4871 15.7989C17.3731 15.7609 17.2522 15.7486 17.133 15.763H16.0347C15.219 15.7522 14.4363 15.4391 13.8382 14.8844C13.5539 14.5944 13.33 14.251 13.1792 13.874C13.0284 13.497 12.9537 13.0939 12.9595 12.6879C12.9704 11.8722 13.2835 11.0895 13.8382 10.4913C14.1281 10.2071 14.4715 9.98315 14.8485 9.83234C15.2256 9.68154 15.6287 9.6069 16.0347 9.61272H19.4393ZM34.2659 34.4335C34.0462 34.763 33.7168 35.2023 33.4971 35.5318C30.0413 37.2235 26.252 38.1239 22.4046 38.1676H22.0751C20.2776 38.6056 18.434 38.8268 16.5838 38.8266H15.7052C13.4946 38.7727 11.3069 38.3648 9.22544 37.6185C10.5349 39.2029 12.0492 40.6063 13.7283 41.7919C13.0694 41.5722 12.5202 41.3526 11.9711 41.1329C11.3336 40.6417 10.7452 40.09 10.2139 39.4855C10.4436 39.8695 10.7006 40.2365 10.9827 40.5838C7.85078 39.052 5.19362 36.6985 3.29481 33.7746C4.57944 34.7513 5.98152 35.563 7.46822 36.1907C7.13874 35.7514 6.91908 35.422 6.5896 34.9827C5.57714 33.4281 4.73051 31.7716 4.06359 30.0405C3.08764 28.9844 2.20568 27.8452 1.42775 26.6358C1.07387 26.1607 0.778853 25.6444 0.549141 25.0983V23.8902C0.560824 20.5618 1.47152 17.2985 3.18498 14.4451C3.83287 16.9176 4.83144 19.2846 6.1503 21.474C7.01548 15.5682 9.88493 10.1396 14.2775 6.09827L15.4856 5.76879C10.6554 10.0125 7.53275 15.8674 6.69943 22.2428C9.03014 25.9592 12.2708 29.0192 16.1145 31.1333C19.9583 33.2474 24.278 34.3455 28.6647 34.3237H29.2139C31.4033 32.8306 33.2571 30.897 34.6565 28.6466C36.0559 26.3961 36.9703 23.8784 37.341 21.2543C37.4379 22.1661 37.4746 23.0834 37.4509 24C37.4384 27.6812 36.3296 31.2753 34.2659 34.3237C32.64 34.6654 30.9849 34.8493 29.3237 34.8728C27.7341 35.9873 25.9967 36.8744 24.1619 37.5087C27.722 37.2766 31.1801 36.2241 34.2659 34.4335Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36307" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36307"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36307" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36307">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-activity" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="27" cy="24" r="18" fill="#FFF4F4"></circle>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5124 5.2H37.4875C40.0605 5.2 42.1759 7.22874 42.2834 9.79945L42.7936 22H45.9964L45.4806 9.66575C45.3014 5.38123 41.7758 2 37.4875 2H10.5124C6.22418 2 2.6986 5.38123 2.51943 9.66575L1.34852 37.6657C1.15843 42.2112 4.79211 46 9.34153 46H38.6585C43.2079 46 46.8416 42.2112 46.6515 37.6657L46.3309 30H43.1281L43.4543 37.7994C43.5683 40.5267 41.3881 42.8 38.6585 42.8H9.34153C6.61188 42.8 4.43167 40.5267 4.54572 37.7994L5.71663 9.79945C5.82413 7.22874 7.93948 5.2 10.5124 5.2Z" fill="#FFCDCB"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M13.9209 16.6399C14.3857 21.7979 18.7206 25.8399 23.9996 25.8399C29.2786 25.8399 33.6136 21.7979 34.0784 16.6399H30.5562C30.1084 19.8606 27.3436 22.3399 23.9996 22.3399C20.6557 22.3399 17.8908 19.8606 17.4431 16.6399H13.9209Z" fill="#FFCDCB"></path>
                <path d="M34.0918 16.6494C34.0918 17.6159 33.3083 18.3994 32.3418 18.3994C31.3753 18.3994 30.5469 17.6159 30.5469 16.6494C30.5469 15.6829 31.3753 14.8994 32.3418 14.8994C33.3083 14.8994 34.0918 15.6829 34.0918 16.6494Z" fill="#FFCDCB"></path>
                <path d="M17.4488 16.6478C17.4488 17.6143 16.6829 18.3978 15.7164 18.3978C14.7499 18.3978 13.9215 17.6143 13.9215 16.6478C13.9215 15.6813 14.7499 14.8978 15.7164 14.8978C16.6829 14.8978 17.4488 15.6813 17.4488 16.6478Z" fill="#FFCDCB"></path>
                <path d="M46 22C46 22.8837 45.2837 23.6 44.4 23.6C43.5163 23.6 42.8 22.8837 42.8 22C42.8 21.1163 43.5163 20.4 44.4 20.4C45.2837 20.4 46 21.1163 46 22Z" fill="#FFCDCB"></path>
                <path d="M46.3309 30.044C46.3309 30.9276 45.6146 31.644 44.7309 31.644C43.8472 31.644 43.1309 30.9276 43.1309 30.044C43.1309 29.1603 43.8472 28.444 44.7309 28.444C45.6146 28.444 46.3309 29.1603 46.3309 30.044Z" fill="#FFCDCB"></path>
            </symbol>
            <symbol id="icon-anbg" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_186_36254" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="68" height="68">
                    <path d="M33.5569 68.5569C52.0898 69.3539 67.7599 54.9761 68.5569 36.4431C69.3539 17.9102 54.9761 2.24013 36.4431 1.44312C17.9102 0.646107 2.24012 15.0239 1.44311 33.5569C0.646096 52.0898 15.0239 67.7599 33.5569 68.5569Z" fill="url(#paint0_linear_186_36254)"></path>
                </mask>
                <g mask="url(#mask0_186_36254)">
                    <g filter="url(#filter0_d_186_36254)">
                        <path d="M33.5569 68.5569C52.0898 69.3539 67.7599 54.9761 68.5569 36.4431C69.3539 17.9102 54.9761 2.24013 36.4431 1.44312C17.9102 0.646107 2.24012 15.0239 1.44311 33.5569C0.646096 52.0898 15.0239 67.7599 33.5569 68.5569Z" fill="url(#paint1_linear_186_36254)"></path>
                    </g>
                    <path d="M34.4543 58.5795C50.119 59.2532 63.3639 47.1005 64.0376 31.4358C64.7113 15.771 52.5586 2.52608 36.8938 1.85241C21.2291 1.17874 7.98416 13.3314 7.31049 28.9962C6.63683 44.6609 18.7895 57.9059 34.4543 58.5795Z" fill="url(#paint2_linear_186_36254)"></path>
                    <g filter="url(#filter1_f_186_36254)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.9941 54.2005C33.1553 63.2088 51.9376 59.0321 60.9459 44.8714C62.7462 42.0417 64.0197 39.0272 64.7923 35.9438C64.3629 40.3673 62.913 44.7595 60.3637 48.766C51.7109 62.3679 33.6703 66.38 20.0681 57.7272C13.8697 53.784 9.66276 47.891 7.73828 41.3576C10.0995 46.4562 13.9011 50.9604 18.9941 54.2005Z" fill="#F26565"></path>
                    </g>
                    <path d="M34.4918 59.275C48.0826 59.8595 59.574 49.3158 60.1585 35.725C60.7429 22.1341 50.1992 10.6428 36.6084 10.0583C23.0175 9.47381 11.5262 20.0175 10.9417 33.6084C10.3572 47.1992 20.901 58.6906 34.4918 59.275Z" fill="url(#paint3_linear_186_36254)"></path>
                    <path d="M-27.8454 35.321C-32.8649 44.6572 -29.3655 56.2949 -20.0292 61.3144C-10.693 66.3338 0.944652 62.8344 5.96414 53.4982C10.9836 44.1619 7.4842 32.5243 -1.85205 27.5048C-11.1883 22.4853 -22.8259 25.9847 -27.8454 35.321Z" fill="url(#paint4_linear_186_36254)"></path>
                    <path d="M94.1708 28.2582C100.556 35.7018 99.6987 46.9127 92.2551 53.2983C84.8115 59.6839 73.6007 58.8262 67.215 51.3826C60.8294 43.939 61.6871 32.7282 69.1307 26.3425C76.5743 19.9569 87.7852 20.8146 94.1708 28.2582Z" fill="url(#paint5_linear_186_36254)"></path>
                    <path d="M24.5591 -25.4224C32.3161 -31.4235 43.4692 -30 49.4703 -22.243C55.4713 -14.486 54.0479 -3.33291 46.2909 2.66816C38.5339 8.66923 27.3808 7.24577 21.3797 -0.511225C15.3786 -8.26822 16.8021 -19.4213 24.5591 -25.4224Z" fill="url(#paint6_linear_186_36254)"></path>
                    <path d="M23.1952 79.4598C22.7739 89.2581 30.3753 97.5427 40.1736 97.9641C49.9719 98.3855 58.2566 90.784 58.6779 80.9857C59.0993 71.1875 51.4978 62.9028 41.6996 62.4814C31.9013 62.06 23.6166 69.6615 23.1952 79.4598Z" fill="url(#paint7_linear_186_36254)"></path>
                    <g filter="url(#filter2_f_186_36254)">
                        <path d="M31.4831 61.6469C31.5333 62.5288 33.3613 63.142 35.5661 63.0166C37.7709 62.8911 39.5175 62.0745 39.4673 61.1925C39.4171 60.3106 37.5891 59.6974 35.3844 59.8229C33.1796 59.9483 31.4329 60.765 31.4831 61.6469Z" fill="white"></path>
                    </g>
                    <g style="mix-blend-mode:screen" filter="url(#filter3_f_186_36254)">
                        <path d="M34.2076 25.4474C40.7458 26.0919 46.4148 22.8729 46.8697 18.2578C47.3245 13.6426 42.3931 9.37889 35.8549 8.73447C29.3168 8.09004 23.6478 11.309 23.193 15.9241C22.7381 20.5393 27.6695 24.803 34.2076 25.4474Z" fill="#FFCFCE"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_186_36254" x="-2.58838" y="-4.5885" width="75.1768" height="75.177" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="-2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.630158 0 0 0 0 0.630158 0 0 0 1 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_186_36254"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_186_36254" result="shape"></feblend>
                    </filter>
                    <filter id="filter1_f_186_36254" x="4.73828" y="32.9438" width="63.0542" height="32.3481" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="1.5" result="effect1_foregroundBlur_186_36254"></fegaussianblur>
                    </filter>
                    <filter id="filter2_f_186_36254" x="25.4819" y="53.8066" width="19.9863" height="15.2263" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="3" result="effect1_foregroundBlur_186_36254"></fegaussianblur>
                    </filter>
                    <filter id="filter3_f_186_36254" x="18.1641" y="3.65234" width="33.7349" height="26.8772" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="2.5" result="effect1_foregroundBlur_186_36254"></fegaussianblur>
                    </filter>
                    <lineargradient id="paint0_linear_186_36254" x1="20.1375" y1="8.34639" x2="44.0641" y2="66.2072" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F22427"></stop>
                        <stop offset="1" stop-color="#960204"></stop>
                    </lineargradient>
                    <lineargradient id="paint1_linear_186_36254" x1="29.9357" y1="6.31369" x2="44.0641" y2="66.2072" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FB444C"></stop>
                        <stop offset="1" stop-color="#DF242E"></stop>
                    </lineargradient>
                    <lineargradient id="paint2_linear_186_36254" x1="28.9373" y1="3.46916" x2="38.5326" y2="60.4738" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FA999A"></stop>
                        <stop offset="0.743552" stop-color="#FE474D"></stop>
                        <stop offset="1" stop-color="#DD2223" stop-opacity="0"></stop>
                    </lineargradient>
                    <lineargradient id="paint3_linear_186_36254" x1="30.1694" y1="11.6563" x2="35.5173" y2="58.6479" gradientUnits="userSpaceOnUse">
                        <stop offset="0.349342" stop-color="#FBF9FD"></stop>
                        <stop offset="0.889385" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint4_linear_186_36254" x1="9.74742" y1="40.5508" x2="-28.0715" y2="49.2971" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.373872" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint5_linear_186_36254" x1="62.1375" y1="40.3055" x2="77.8119" y2="39.7457" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.601592" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint6_linear_186_36254" x1="36.9468" y1="5.59797" x2="36.9468" y2="-11.196" gradientUnits="userSpaceOnUse">
                        <stop offset="0.0664686" stop-color="#FBF9FD"></stop>
                        <stop offset="1" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint7_linear_186_36254" x1="56.6154" y1="68.6771" x2="28.6354" y2="91.1926" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#EBA3A6"></stop>
                        <stop offset="0.373872" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                </defs>
            </symbol>
            <symbol id="icon-changlong" viewBox="0 0 98 101" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_i_987_21)">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M19.2127 19.7153C12.9535 24.0685 2.39277 35.0529 0.515006 51.2337C-1.93657 72.3592 12.7729 96.4571 38.3545 100.385C58.8198 103.527 74.3109 91.5738 79.4983 85.2043C91.01 69.9175 89.7309 57.2848 89.7309 57.2848C89.7309 57.2848 88.5584 63.7604 84.0816 67.5821C78.5846 72.2749 74.7017 72.3592 73.849 72.3592C71.5395 70.4838 65.5989 65.6925 59.8857 62.805C52.7441 59.1956 42.1917 53.1446 42.5115 47.1998C42.8341 41.2033 48.5871 38.6011 58.7132 39.3442C59.033 39.3676 56.5814 43.8026 54.1298 45.8197C58.429 46.3859 68.2637 47.943 71.9304 50.9154C75.5971 53.8878 74.7372 56.4002 73.849 57.2848C74.9504 57.3556 77.7289 57.1362 80.0312 55.6925C82.3335 54.2487 83.4776 52.5431 83.7619 51.8708C82.8381 52.26 80.7987 52.7201 80.0312 51.4462C79.2638 50.1723 79.4272 48.9338 79.6048 48.4737C76.4071 48.0845 70.3102 46.5841 71.504 43.6966C72.9963 40.0873 77.2599 39.9811 78.6455 40.4057C80.0312 40.8304 82.696 41.892 83.0157 47.6245C83.028 47.8438 83.3137 47.6318 83.8062 47.2665C84.9475 46.4199 87.1993 44.7495 89.7309 45.7136C91.5429 46.4037 91.2587 50.3138 89.7309 51.4462C91.5429 51.0923 96.2329 48.686 97.4054 42.9535C97.4921 42.5296 97.0757 42.478 96.2514 42.376C94.8508 42.2027 92.2727 41.8836 88.9848 39.3442C84.8064 36.117 83.6197 35.1686 83.5487 35.0979L83.7619 31.17C83.328 30.8937 82.8607 30.5972 82.3688 30.2851C78.3885 27.76 72.798 24.2134 70.3315 22.0404C67.8009 19.811 62.4438 13.4416 60.9516 0.915029C60.6652 -1.48888 59.6725 14.1848 63.0205 20.7666C63.6774 22.058 49.5901 9.72613 47.7781 4.20592C47.7093 3.99639 48.9069 12.0616 54.4496 19.3865C55.0004 20.1144 54.0893 19.9955 52.1669 19.7448C48.2017 19.2276 39.9341 18.1493 31.3195 22.7835C16.7167 30.6392 15.5442 44.8644 16.7167 51.4462C17.5677 56.2233 19.2749 62.9112 28.1218 71.6161C30.0053 73.4694 24.6044 78.3041 15.4376 72.3592C10.1081 68.9029 4.24566 63.3357 5.31156 48.4737C6.43875 32.7572 16.0384 22.2296 19.2127 19.7153ZM72.7831 29.896L80.0312 33.2931L77.3665 34.8854C76.8335 34.1423 75.5971 32.4863 74.9149 31.8069C74.2327 31.1275 73.2094 30.2499 72.7831 29.896Z" fill="url(#paint0_linear_987_21)"></path>
                </g>
                <mask id="mask0_987_21" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="98" height="101">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M19.2127 19.7153C12.9535 24.0685 2.39277 35.0529 0.515006 51.2337C-1.93657 72.3592 12.7729 96.4571 38.3545 100.385C58.8198 103.527 74.3109 91.5738 79.4983 85.2043C91.01 69.9175 89.7309 57.2848 89.7309 57.2848C89.7309 57.2848 88.5584 63.7604 84.0816 67.5821C78.5846 72.2749 74.7017 72.3592 73.849 72.3592C71.5395 70.4838 65.5989 65.6925 59.8857 62.805C52.7441 59.1956 42.1917 53.1446 42.5115 47.1998C42.8341 41.2033 48.5871 38.6011 58.7132 39.3442C59.033 39.3676 56.5814 43.8026 54.1298 45.8197C58.429 46.3859 68.2637 47.943 71.9304 50.9154C75.5971 53.8878 74.7372 56.4002 73.849 57.2848C74.9504 57.3556 77.7289 57.1362 80.0312 55.6925C82.3335 54.2487 83.4776 52.5431 83.7619 51.8708C82.8381 52.26 80.7987 52.7201 80.0312 51.4462C79.2638 50.1723 79.4272 48.9338 79.6048 48.4737C76.4071 48.0845 70.3102 46.5841 71.504 43.6966C72.9963 40.0873 77.2599 39.9811 78.6455 40.4057C80.0312 40.8304 82.696 41.892 83.0157 47.6245C83.028 47.8438 83.3137 47.6318 83.8062 47.2665C84.9475 46.4199 87.1993 44.7495 89.7309 45.7136C91.5429 46.4037 91.2587 50.3138 89.7309 51.4462C91.5429 51.0923 96.2329 48.686 97.4054 42.9535C97.4921 42.5296 97.0757 42.478 96.2514 42.376C94.8508 42.2027 92.2727 41.8836 88.9848 39.3442C84.8064 36.117 83.6197 35.1686 83.5487 35.0979L83.7619 31.17C83.328 30.8937 82.8607 30.5972 82.3688 30.2851C78.3885 27.76 72.798 24.2134 70.3315 22.0404C67.8009 19.811 62.4438 13.4416 60.9516 0.915029C60.6652 -1.48888 59.6725 14.1848 63.0205 20.7666C63.6774 22.058 49.5901 9.72613 47.7781 4.20592C47.7093 3.99639 48.9069 12.0616 54.4496 19.3865C55.0004 20.1144 54.0893 19.9955 52.1669 19.7448C48.2017 19.2276 39.9341 18.1493 31.3195 22.7835C16.7167 30.6392 15.5442 44.8644 16.7167 51.4462C17.5677 56.2233 19.2749 62.9112 28.1218 71.6161C30.0053 73.4694 24.6044 78.3041 15.4376 72.3592C10.1081 68.9029 4.24566 63.3357 5.31156 48.4737C6.43875 32.7572 16.0384 22.2296 19.2127 19.7153ZM72.7831 29.896L80.0312 33.2931L77.3665 34.8854C76.8335 34.1423 75.5971 32.4863 74.9149 31.8069C74.2327 31.1275 73.2094 30.2499 72.7831 29.896Z" fill="url(#paint1_linear_987_21)"></path>
                </mask>
                <g mask="url(#mask0_987_21)">
                    <path d="M45.4704 37.7506C50.339 36.9819 56.0944 38.4209 58.9167 39.1666C57.0834 40.9999 43.142 41.4792 43.0362 46.5926C42.9492 50.7943 47.9613 56.1424 55.2076 59.6957C67.5906 65.7679 72.4592 70.5617 74.1526 72.0531C81.0045 78.0877 80.0289 84.5368 80.0289 84.5368C80.0289 84.5368 77.2619 88.7758 71.335 92.7192C65.4081 96.6626 60.2917 98.2916 60.2917 98.2916C60.2917 98.2916 71.6125 89.0979 57.6418 77.8057C51.2747 72.6593 32.7699 59.8022 32.9815 50.5342C33.1737 42.1206 40.0727 38.6029 45.4704 37.7506Z" fill="url(#paint2_linear_987_21)"></path>
                    <path d="M78.3867 77.393C76.7916 74.4583 74.4968 72.913 73.5833 72.1666C74.7892 72.1666 80.2504 71.3153 85.0742 66.6239C88.0152 63.7635 89.3068 59.2668 89.7453 56.921C89.855 59.2668 90.2268 63.2119 88.4727 68.6497C86.6387 74.3353 83.5369 80.5917 80.0287 84.5368C80.0287 82.4043 79.6889 79.7887 78.3867 77.393Z" fill="url(#paint3_linear_987_21)"></path>
                    <g filter="url(#filter1_i_987_21)">
                        <path d="M38.7881 80.8169C36.1408 78.3327 30.2516 73.6504 27.75 71.25C27.4284 78.4512 6.32013 74.357 5.44528 52.7534C4.69479 34.2209 14.7727 23.8427 20.1333 19.0771C13.0573 23.4191 0.942384 36.9743 0.299115 52.7534C-0.278927 66.9325 4.26595 78.2753 12.8429 86.8532C21.9493 95.9606 32.912 99.6843 38.0939 100.39C40.0237 99.8608 43.1248 96.2783 44.0415 92.2541C45.1753 87.2768 40.9324 82.829 38.7881 80.8169Z" fill="url(#paint4_linear_987_21)"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_i_987_21" x="0.25" y="-1.33337" width="97.1667" height="102.235" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="-2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.980392 0 0 0 0 0.898039 0 0 0 0 0.623529 0 0 0 1 0"></fecolormatrix>
                        <feblend mode="normal" in2="shape" result="effect1_innerShadow_987_21"></feblend>
                    </filter>
                    <filter id="filter1_i_987_21" x="0.25" y="17.0771" width="43.9807" height="83.3131" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="-2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.980392 0 0 0 0 0.898039 0 0 0 0 0.623529 0 0 0 1 0"></fecolormatrix>
                        <feblend mode="normal" in2="shape" result="effect1_innerShadow_987_21"></feblend>
                    </filter>
                    <lineargradient id="paint0_linear_987_21" x1="48.8333" y1="0.666626" x2="48.8333" y2="100.902" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D9AC4F"></stop>
                        <stop offset="1" stop-color="#8F5206"></stop>
                    </lineargradient>
                    <lineargradient id="paint1_linear_987_21" x1="48.8333" y1="0.666626" x2="48.8333" y2="100.902" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF736D"></stop>
                        <stop offset="1" stop-color="#F73E46"></stop>
                    </lineargradient>
                    <lineargradient id="paint2_linear_987_21" x1="56.4835" y1="37.4526" x2="56.4835" y2="97.8333" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#8F5206"></stop>
                        <stop offset="1" stop-color="#D9AC4F"></stop>
                    </lineargradient>
                    <lineargradient id="paint3_linear_987_21" x1="90.3364" y1="58.3071" x2="80.0916" y2="83.5663" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#D9AC4F"></stop>
                        <stop offset="1" stop-color="#8F5206"></stop>
                    </lineargradient>
                    <lineargradient id="paint4_linear_987_21" x1="22.2404" y1="19.0771" x2="22.2404" y2="99.8789" gradientUnits="userSpaceOnUse">
                        <stop offset="0.111391" stop-color="#D9AC4F"></stop>
                        <stop offset="1" stop-color="#8F5206"></stop>
                    </lineargradient>
                </defs>
            </symbol>
            <symbol id="icon-chat" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="30" cy="24" r="18" fill="#FFF4F4"></circle>
                <line x1="17.4375" y1="19.125" x2="17.4375" y2="23.375" stroke="#FFCDCB" stroke-width="4" stroke-linecap="round"></line>
                <line x1="29.125" y1="19.125" x2="29.125" y2="23.375" stroke="#FFCDCB" stroke-width="4" stroke-linecap="round"></line>
                <path d="M39.5469 32.0192C42.1287 28.9816 43.6344 25.2175 43.6344 21.2328C43.6344 11.6544 34.9341 3.35014 23.5 3.35014C12.0659 3.35014 3.36559 11.6544 3.36559 21.2328C3.36559 29.4476 9.76503 36.7251 18.8 38.6308V42.0409C8.07347 40.0735 0 31.505 0 21.2328C0 9.50625 10.5213 0 23.5 0C36.4787 0 47 9.50625 47 21.2328C47 26.1009 45.1868 30.5864 42.1373 34.1679L39.5469 32.0192Z" fill="#FFCDCB"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M32.8274 39.1807C33.4277 39.8869 33.3392 40.9439 32.6297 41.5414L27.2077 47.1082C25.7525 48.3339 23.6088 48.2916 22.2035 47.0094L17.2588 41.4975C16.5737 40.8724 16.5274 39.8128 17.1554 39.1308C17.7834 38.4489 18.8479 38.4028 19.533 39.0279L24.4777 44.5398C24.6339 44.6823 24.8721 44.687 25.0338 44.5508L30.4558 38.984C31.1652 38.3864 32.2271 38.4745 32.8274 39.1807Z" fill="#FFCDCB"></path>
                <path d="M41.9426 31.931C42.6223 32.5619 42.6595 33.6218 42.0257 34.2984C41.3918 34.975 40.327 35.0121 39.6473 34.3811C38.9676 33.7502 38.9304 32.6903 39.5642 32.0137C40.198 31.3371 41.2629 31.3001 41.9426 31.931Z" fill="#FFCDCB"></path>
            </symbol>
            <symbol id="icon-close" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M30 57C44.9117 57 57 44.9117 57 30C57 15.0883 44.9117 3 30 3C15.0883 3 3 15.0883 3 30C3 44.9117 15.0883 57 30 57Z" stroke="white" stroke-width="4" stroke-linejoin="round"></path>
                <path d="M43 17L17 43" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M17 17L43 43" stroke="white" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"></path>
            </symbol>
            <symbol id="icon-copy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none">
                <path d="M6.5 6.2158V3.90625C6.5 3.1296 7.1296 2.5 7.90625 2.5H20.0938C20.8704 2.5 21.5 3.1296 21.5 3.90625V16.0938C21.5 16.8704 20.8704 17.5 20.0938 17.5H17.7582" stroke="#87C7AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M16.0938 6.5H3.90625C3.1296 6.5 2.5 7.1296 2.5 7.90625V20.0938C2.5 20.8704 3.1296 21.5 3.90625 21.5H16.0938C16.8704 21.5 17.5 20.8704 17.5 20.0938V7.90625C17.5 7.1296 16.8704 6.5 16.0938 6.5Z" fill="#87C7AF" stroke="#87C7AF" stroke-width="2" stroke-linejoin="round"></path>
            </symbol>
            <symbol id="icon-down" viewBox="0 0 48 48" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M23.9999 29.0001L12 17.0001H19.9999V6.00012H27.9999V17.0001H35.9999L23.9999 29.0001Z" fill="currentColor" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                <path d="M42 37H6" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path>
                <path d="M34 44H14" stroke="currentColor" stroke-width="2" stroke-linecap="round"></path>
            </symbol>
            <!-- #FB5755 -->
            <symbol id="icon-errorTip" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 27.5C18.4517 27.5 21.5768 26.1009 23.8388 23.8388C26.1009 21.5768 27.5 18.4517 27.5 15C27.5 11.5482 26.1009 8.42325 23.8388 6.16116C21.5768 3.89911 18.4517 2.5 15 2.5C11.5482 2.5 8.42325 3.89911 6.16116 6.16116C3.89911 8.42325 2.5 11.5482 2.5 15C2.5 18.4517 3.89911 21.5768 6.16116 23.8388C8.42325 26.1009 11.5482 27.5 15 27.5Z" stroke="#FE6868" stroke-linejoin="round"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M15 23.125C15.8629 23.125 16.5625 22.4254 16.5625 21.5625C16.5625 20.6996 15.8629 20 15 20C14.1371 20 13.4375 20.6996 13.4375 21.5625C13.4375 22.4254 14.1371 23.125 15 23.125Z" fill="#FF7172"></path>
                <path d="M15 7.5V17.5" stroke="#FE6868" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </symbol>
            <!--?xml version="1.0" standalone="no"?-->
            <symbol id="icon-eye" t="1503993826520" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7878" xmlns:xlink="http://www.w3.org/1999/xlink">
                <defs>
                    <style type="text/css"></style>
                </defs>
                <path d="M941.677063 391.710356c9.337669-14.005992 6.224772-32.68133-6.224772-43.575447-14.005992-10.894118-32.68133-7.78122-43.575447 6.224771-1.556449 1.556449-174.300768 205.426673-379.727441 205.426673-199.200878 0-379.727441-205.426673-381.28389-206.982098-10.894118-12.450567-31.124881-14.005992-43.575448-3.112898-12.450567 10.894118-14.005992 31.124881-3.112897 43.575448 3.112897 4.668323 40.46255 46.687322 99.600439 93.375667l-79.369676 82.48155c-12.450567 12.450567-10.894118 32.68133 1.556449 43.575448 3.112897 6.224772 10.894118 9.337669 18.675338 9.337669 7.78122 0 15.562441-3.112897 21.787213-9.337669l85.594447-88.706321c40.46255 28.013007 88.706321 54.469566 141.619438 73.14388L340.959485 707.631586c-4.668323 17.118889 4.669346 34.237779 21.787213 38.906101h9.337669c14.005992 0 26.456558-9.337669 29.568432-23.343661l32.68133-110.494556c24.90011 4.668323 51.356668 7.78122 77.813227 7.78122s52.913117-3.112897 77.813227-7.78122l32.68133 108.938108c3.112897 14.005992 17.118889 23.343661 29.569456 23.343661 3.112897 0 6.224772 0 7.78122-1.556449 17.118889-4.669346 26.456558-21.787212 21.788236-38.906102l-32.68133-108.938108c52.913117-18.675338 101.156888-45.131897 141.619438-73.14388l84.037998 87.150896c6.224772 6.224772 14.005992 9.337669 21.787212 9.337669 7.78122 0 15.562441-3.112897 21.787212-9.337669 12.450567-12.450567 12.450567-31.124881 1.556449-43.575448l-79.369675-82.48155c63.808258-46.688345 101.158934-91.820242 101.158934-91.820242z" p-id="7879"></path>
            </symbol>
            <symbol id="icon-AG" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36299)">
                    <g filter="url(#filter0_d_1701_36299)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M4.41379 12.2759H26.6207L27.5675 8H54.3448L49.6552 36.8276H27.8621L27.7241 40H0L4.41379 12.2759ZM45.3793 19.5863H49.1036C48.8737 17.2414 45.642 12.8538 41.2418 12.4138C35.7243 11.862 30.8967 14.2761 28.8279 20.1379C26.3453 27.1723 30.7612 32.7907 36.9656 32.9655C46.7588 33.2413 48.2759 23.1724 48.2759 23.1724H38.069V26.3449H43.1725C43.0347 27.4483 41.2418 29.5829 36.9656 29.3793C32 29.1428 31.8621 24.9655 32 23.1724C32.1379 21.3793 34.3448 15.3102 40 15.724C43.3105 15.9662 44.8277 17.793 45.3793 19.5863ZM17.3795 14.7585L2.34498 35.862H6.62084L10.8967 29.9309L20.1381 30.0689L22.3262 35.862L23.5864 28.8275L18.345 14.7585H17.3795ZM17.2415 21.3792H16.6898L13.3795 26.4826H18.8967L17.2415 21.3792Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36299" x="-4" y="6" width="62.3448" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36299"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36299" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36299">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-BB" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36345)">
                    <g filter="url(#filter0_d_1701_36345)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M27.1216 13.2603L24.8091 8H42.8884C44.7805 8.07306 48.9429 9.2274 50.4565 13.2603C51.9701 17.2932 49.966 19.7626 48.7747 20.4932V21.1507C49.6156 22.1005 50.877 24.4384 50.877 26.411C50.877 28.1644 49.1952 32.5479 44.9907 33.4247C42.1899 34.0087 38.0288 34.6839 35.1773 35.1465C34.6043 35.2395 34.0841 35.3239 33.6386 35.3973L34.4795 29.9178L42.2578 28.8219C43.2388 28.6758 45.1168 27.8575 44.7805 25.7534C44.4441 23.6493 42.3979 23.4155 41.4169 23.5616L33.6386 24L31.7465 35.6164C30.6254 35.8356 28.2989 36.2301 27.9625 36.0548C27.9218 36.0336 27.8674 36.0083 27.8023 35.9781C27.1956 35.696 25.6704 34.9872 25.8603 33.2055C26.0284 31.6274 28.0326 20.5662 29.0136 15.2329C29.1538 14.5753 28.9716 13.2603 27.1216 13.2603ZM35.5306 13.0411H42.468C43.3089 13.0411 44.7805 14.0493 44.7805 15.4521C44.7805 16.8548 43.5191 18.0822 42.6782 18.0822L34.4795 18.5205L35.5306 13.0411Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0.00266822 8H17.0308C18.5026 8 22.7069 9.49041 24.3887 12.8219C26.4909 16.9863 23.3378 21.3699 22.4967 22.0274C22.3088 22.1743 22.3565 22.5388 22.4967 22.6849C23.3376 23.5616 25.0194 26.0164 25.0194 28.8219C25.0194 32.3288 21.6558 35.8356 20.1842 36.4932C18.7126 37.1507 8.83229 38.9041 7.36052 38.9041C7.1505 38.9041 8.20141 33.4247 8.20141 33.4247L15.7695 32.3288C16.6805 32.1096 18.5865 31.2329 18.9228 29.4795C19.3433 27.2877 17.8717 25.3151 15.5593 24.8767C13.7093 24.526 9.46276 24.8767 7.57074 25.0959L6.51962 31.8904L0.633341 32.5479L3.36626 14.5753C3.29618 14.0639 2.81966 13.0848 1.47424 13.2603C1.26422 13.2877 -0.0674065 11.1416 0.00266822 9.9726V8ZM9.67319 13.0409H15.4806C17.4515 13.0411 19.1333 14.3562 19.1333 15.8902C19.1333 17.8749 17.6617 19.1779 16.6106 19.397C15.7697 19.5724 10.9345 19.7623 8.62207 19.8354L9.67319 13.0409Z" fill="white"></path>
                        <path d="M6.09919 33.6438L0.212914 34.5206C0.142839 35.3242 0.00268981 37.1507 0.00268981 38.0274C0.00268981 39.1233 0.843587 39.7808 1.89471 39.7808C2.7356 39.7808 4.48747 39.3425 5.2583 39.1233L6.09919 33.6438Z" fill="white"></path>
                        <path d="M50.6667 40H12.4059C22.4967 38.4658 43.2668 35.3096 45.6213 34.9589C47.9759 34.6082 49.1251 35.9817 49.4054 36.7123L50.6667 39.5616V40Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36345" x="-4" y="6" width="59" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36345"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36345" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36345">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-CARD365" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36342)">
                    <g filter="url(#filter0_d_1701_36342)">
                        <path d="M8.9014 13.223V9.42233C10.5813 9.28156 13.9412 9.4223 16.0412 11.1115C17.9534 12.6498 18.9811 14.4899 18.9811 18.7129C18.9811 22.5136 13.5212 24.2029 14.7812 24.2029C16.0412 24.2029 19.821 25.4698 19.821 29.6928C19.821 33.9158 18.6724 35.8378 17.3011 36.8719C15.6212 38.1388 11.2813 39.1242 9.32138 38.9834V35.605C10.5813 35.605 11.0013 34.4789 11.0013 33.9158V28.0036C11.0013 26.3144 11.0013 25.4698 9.74137 25.4698C8.7334 25.4698 8.06142 25.4698 7.64144 24.6252C7.36145 23.7806 7.22145 22.0914 8.9014 22.0914C10.5813 22.0914 11.0013 21.2468 11.0013 20.8245V14.0676C11.0013 13.392 9.60137 13.223 8.9014 13.223Z" fill="white"></path>
                        <path d="M33.2606 12.8007V9.4223C23.6009 10.6892 22.7609 20.8245 22.7609 21.2467C22.7609 21.669 21.921 29.2705 25.2808 34.3381C27.6676 37.9379 31.4406 38.9834 32.8406 38.9834V35.605C32.5606 35.605 31.9166 35.5205 31.5806 35.1827C31.2446 34.8448 31.1606 34.1973 31.1606 33.9158V14.4899C31.1606 13.1386 32.5606 12.8007 33.2606 12.8007Z" fill="white"></path>
                        <path d="M36.2005 21.669C33.5126 21.669 32.0006 22.5136 31.5806 22.9359V26.7367L33.2606 25.8921C33.9326 25.5542 34.5205 25.8921 34.5205 27.159V33.4935C34.5205 35.5205 33.6806 35.605 33.2606 35.605V38.9834C33.2606 38.9834 42.0803 38.5611 42.5003 29.2705C42.7291 24.2081 39.5604 21.669 36.2005 21.669Z" fill="white"></path>
                        <path d="M57.1998 10.6892C53.7733 10.6892 49.22 9.98536 47.1201 9.4223L46.7001 17.0237C46.7001 17.0237 55.5198 18.7129 59.2997 15.7568C61.6718 13.9017 63.0796 10.6892 63.4995 9C63.4995 9 60.5596 10.6892 57.1998 10.6892Z" fill="white"></path>
                        <path d="M49.64 18.713L46.7001 18.2906L46.2801 23.3582C46.2801 24.2028 46.2925 25.0599 46.7001 25.4698C47.5401 26.3144 48.8 25.8921 50.06 25.0475C51.068 24.3718 52.4399 23.9213 52.9999 23.7805V19.9798C52.9999 19.9798 49.92 21.106 49.22 21.669L49.64 18.713Z" fill="white"></path>
                        <path d="M53.4199 23.7805V19.9798C59.2997 19.5575 63.4523 22.8832 63.9195 27.5813C64.3395 31.8043 63.0796 35.1827 60.9796 36.8719C59.1245 38.3642 54.8781 39.1242 53.3382 38.9834V36.0273C53.3382 36.0273 55.9398 35.8865 55.9398 33.4935V25.8921C55.9398 24.2029 53.9799 23.7805 53.4199 23.7805Z" fill="white"></path>
                        <path d="M8.48133 13.223V9.4223C3.02152 9.8446 1.34158 13.6453 0.92159 15.7568C0.501604 17.8683 1.76156 21.2467 4.70146 21.2467C7.64136 21.2467 9.32131 19.1352 9.32131 17.0237C9.32131 14.9122 7.22138 14.0676 7.22138 13.6453C7.22138 13.3075 8.06135 13.223 8.48133 13.223Z" fill="white"></path>
                        <path d="M33.6805 12.8007V9.4223C39.1403 9.8446 41.2402 12.8007 41.6602 14.9122C42.1811 17.5313 40.8202 20.8244 37.8803 20.8244C34.9404 20.8244 33.6806 18.7129 33.6806 17.0237C33.6806 14.9122 35.3604 14.0676 35.3604 13.6453C35.3604 12.8844 34.1005 12.8007 33.6805 12.8007Z" fill="white"></path>
                        <path d="M8.98005 35.605V38.9834C3.52024 38.5611 0.501624 35.605 0.0814105 33.0712C-0.407067 30.1257 1.34158 27.159 4.28148 27.159C7.22138 27.159 8.48132 29.2705 8.48132 31.382C8.48132 33.4935 6.80138 34.7604 6.80138 35.1827C6.80138 35.5205 8.56007 35.605 8.98005 35.605Z" fill="white"></path>
                        <path d="M52.9998 36.0273L53.0003 38.9834C47.5405 38.5611 45.96 35.1602 45.4405 33.0712C44.8494 30.6944 46.2807 27.5813 48.8004 27.5813C51.7403 27.5813 53.0003 29.2705 53.0003 31.382C53.0003 33.4935 50.8999 34.3381 50.9004 35.1827C50.9006 35.5205 52.5798 36.0273 52.9998 36.0273Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36342" x="-4" y="7" width="72" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36342"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36342" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36342">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-CMD" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36303)">
                    <g filter="url(#filter0_d_1701_36303)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M27.9793 18.0423L29.5005 15H10.7403C7.19096 15 4.90927 18.0423 4.40223 18.8028C3.89519 19.5634 0.345823 26.1549 1.10639 29.1972C1.71484 31.631 4.14857 32.493 5.6697 32.493H21.388L22.6556 29.4507C20.4955 29.389 16.4875 29.4174 13.6928 29.4372C12.6608 29.4446 11.7942 29.4507 11.2472 29.4507C9.21907 29.4507 9.10658 27.6024 9.47229 26.662C10.0638 25.1408 11.5005 21.693 12.5145 20.0704C13.5286 18.4479 14.6272 18.0423 15.0498 18.0423H27.9793ZM31.5289 33H26.205L33.8106 15L46.7402 15.2535L46.9937 26.4085L56.6275 15L69.8106 15.2535L62.712 33H52.5712L57.3881 20.5775L46.7402 33H38.374L37.3599 19.5634L31.5289 33ZM67.5289 33L73.867 15.2535H94.6557C95.6698 15.2535 97.698 15.7606 98.4586 17.0282C99.211 18.2823 98.543 21.507 97.9515 22.8592L95.1627 29.4507C94.4022 30.9718 91.5121 33 88.0642 33H67.5289ZM81.2192 18.549H88.5714C88.9939 18.6335 89.7375 19.1068 89.3319 20.3237C88.9263 21.5406 86.9657 26.4082 86.0361 28.6899C85.8671 29.1124 85.0728 29.9575 83.2474 29.9575H76.9094L81.2192 18.549Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36303" x="-3" y="13" width="105.794" height="26" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36303"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36303" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36303">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-CQ9" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36284)">
                    <g filter="url(#filter0_d_1701_36284)">
                        <path d="M26.6175 26.0063C26.646 26.3494 26.6807 26.6836 26.7147 27.01C26.9095 28.8826 27.0776 30.4992 25.8895 32.0676C22.349 36.4566 17.6197 37.7733 13.2766 37.7733C5.94416 37.7733 0 31.8782 0 24.6061C0 17.3341 5.94416 11.4389 13.2766 11.4389C16.9785 11.4389 20.6049 13.4043 23.0128 15.828C22.349 16.7058 21.2426 17.8031 20.7754 18.4471C18.9896 16.3108 16.2933 14.9502 13.2766 14.9502C7.89949 14.9502 3.54044 19.2733 3.54044 24.6061C3.54044 29.939 7.89949 34.2621 13.2766 34.2621C16.3001 34.2621 20.2875 33.3073 23.0128 30.3119C24.2109 28.9952 24.0845 28.6227 24.1192 28.1174C25.2229 27.2257 25.7835 26.4712 26.6175 26.0063Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M36.732 37.7733C44.0645 37.7733 50.0087 31.8782 50.0087 24.6061C50.0087 17.3341 44.0645 11.4389 36.732 11.4389C29.3996 11.4389 23.4554 17.3341 23.4554 24.6061C23.4554 31.8782 29.3996 37.7733 36.732 37.7733ZM36.732 34.2621C42.1092 34.2621 46.4683 29.939 46.4683 24.6061C46.4683 19.2733 42.1092 14.9502 36.732 14.9502C31.3549 14.9502 26.9958 19.2733 26.9958 24.6061C26.9958 29.939 31.3549 34.2621 36.732 34.2621Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M63.2853 32.0676C69.1513 32.0676 73.9066 27.3514 73.9066 21.5338C73.9066 15.7161 69.1513 11 63.2853 11C57.4193 11 52.664 15.7161 52.664 21.5338C52.664 27.3514 57.4193 32.0676 63.2853 32.0676ZM63.2853 28.5563C67.196 28.5563 70.3662 25.4122 70.3662 21.5338C70.3662 17.6553 67.196 14.5113 63.2853 14.5113C59.3747 14.5113 56.2045 17.6553 56.2045 21.5338C56.2045 25.4122 59.3747 28.5563 63.2853 28.5563Z" fill="white"></path>
                        <path d="M36.732 26.1423C33.1916 26.1423 30.3888 26.5081 29.4299 26.8007C29.6512 27.6785 30.7576 30.3119 30.7576 30.3119C30.7576 30.3119 40.8981 28.2098 46.4683 32.5065C54.4342 38.6512 61.0726 38.2123 63.7279 37.7733C66.3832 37.3344 70.1449 36.0177 72.3577 31.8481C74.5705 27.6785 73.9066 21.5338 73.9066 21.5338H72.3577L69.2598 29.873C69.4811 30.9703 66.2947 33.911 61.5151 34.2621C55.5406 34.701 52.4758 31.9468 48.9023 29.6536C45.1406 27.2396 41.1576 26.1423 36.732 26.1423Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36284" x="-4" y="9" width="82" height="35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36284"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36284" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36284">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-DG" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36315)">
                    <g filter="url(#filter0_d_1701_36315)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 22.0483C1.00073 22.4057 3.34531 23.1205 4.71774 23.1205H22.8382C22.4808 21.4764 22.3021 16.8326 22.3021 11.3261C22.3021 11.2637 22.847 11.2285 23.7069 11.173C25.8015 11.0378 29.7651 10.782 32.2736 9.7178C35.4903 8.35317 38.0636 6.39394 38.5997 5H38.9214C39.3503 6.39394 42.1378 8.75281 45.998 10.0395C48.6174 10.9125 52.4657 10.9664 54.5248 10.9952C54.7414 10.9982 54.9383 11.001 55.1118 11.0044C55.2548 13.4705 55.3262 19.2391 54.4685 23.4422C54.451 23.528 61.4611 23.4079 67.6695 23.3015L67.67 23.3015L67.6706 23.3015L67.6712 23.3015L67.6719 23.3015L67.6732 23.3014C69.2239 23.2749 70.7244 23.2492 72.0528 23.2277C74.9474 23.1811 76.8132 22.3443 77.3066 22.0483C77.4139 21.984 76.1302 26.5579 70.9806 26.6588C65.5123 26.7661 55.9696 26.8732 55.7551 26.8732C55.5407 26.8732 52.324 27.6222 52.324 31.4837C52.324 37.5954 57.3635 37.1664 57.3635 37.1664V32.3415H60.7945V40.4903L56.184 40.5975C54.6829 40.5975 50.8229 39.2037 49.5363 35.022C49.5117 34.942 49.1716 35.3599 48.7003 35.9391L48.7003 35.9392C48.3178 36.4092 47.8488 36.9856 47.3919 37.4882C46.3196 38.6676 42.0308 42.0986 38.8142 42.0986C33.3459 42.0986 27.9848 35.1292 27.9848 35.1292C27.9848 35.1292 27.2342 37.2445 25.197 39.0965C23.8032 40.3637 21.7659 40.5976 21.5515 40.5976L16.5121 40.4903V32.3415H20.0504L20.1576 37.0592H21.6587C22.8382 37.0592 25.0898 35.2365 25.0898 32.8776C25.0898 29.2321 24.232 26.766 20.6937 26.6587C20.2127 26.6442 18.5168 26.6555 16.38 26.6698L16.3787 26.6698L16.3772 26.6698L16.3767 26.6698H16.3763H16.3761H16.376H16.376H16.3759H16.3757H16.3755H16.3754H16.3753C12.7992 26.6937 7.9908 26.7259 5.57551 26.6587C4.0702 26.6169 0.171554 24.7932 0 22.0483ZM2.57331 26.8732C3.25238 27.2306 4.86785 27.9454 5.89718 27.9454H20.0504V31.4837H8.14883C6.50477 31.0191 3.08798 29.4465 2.57331 26.8732ZM8.36327 32.4487C7.29106 32.4487 5.86144 31.8768 5.46829 31.5909C5.36107 33.6282 8.57771 35.8799 10.186 35.987H15.2254V32.4487H8.36327ZM7.93438 35.987C8.36327 36.3444 9.99304 37.0592 10.9366 37.0592H15.2254V40.4903H12.0088C10.9366 40.3832 8.36327 38.5604 7.93438 35.987ZM71.0878 27.8382H57.3635V31.4837H67.9784C73.5539 31.4837 74.8405 26.7874 74.7333 26.8732C73.8755 27.5594 71.9456 27.8024 71.0878 27.8382ZM62.0812 32.3415H68.9434C70.0156 32.3415 71.4095 32.0199 72.0528 31.591C72.305 31.4229 70.7661 35.8798 66.4773 35.8798H62.0812V32.3415ZM66.6917 37.1664H62.0812V40.4903L64.5473 40.4904C68.4073 40.4904 69.5331 35.9871 69.3723 36.0943C68.5497 36.6427 67.335 37.1664 66.6917 37.1664ZM33.1312 12.3986C30.3435 13.4708 26.7337 14.0427 25.1968 14.1142C24.6607 15.0792 25.1968 19.5825 26.3762 24.3002C27.5557 29.018 29.5929 32.6635 30.0218 32.3418C30.2968 32.1355 33.4382 30.915 36.2786 29.8114C37.8672 29.1942 39.3617 28.6135 40.2078 28.2674C42.5667 27.3024 43.1028 26.0158 42.9956 24.0858C42.8883 22.1558 41.6017 21.7269 40.5295 21.5125C39.6717 21.3409 39.3143 21.8699 39.2428 22.1558C40.0291 22.2988 41.28 22.5847 39.9934 22.5847C38.7759 22.5847 38.6644 22.8304 38.589 22.9964C38.5648 23.0497 38.5444 23.0947 38.4923 23.1208C38.2778 23.228 38.385 23.7641 38.4923 24.0858C38.578 24.3431 38.8139 24.5504 38.9212 24.6219H36.6695V24.9436H36.2406C35.2756 24.9436 33.3027 25.2223 33.1312 26.3374C32.9596 27.4525 34.0604 27.8028 34.6323 27.8385L34.8467 28.2674C34.2034 28.2674 32.7452 28.246 32.059 28.1602C31.3728 28.0744 30.272 27.2667 29.8073 26.8735H29.0568C29.021 26.6948 28.971 26.0587 29.0568 24.9436C29.164 23.5497 30.0218 22.7991 30.2362 23.1208C30.297 23.2119 30.3147 23.3203 30.3308 23.4191C30.3717 23.6688 30.4025 23.857 31.094 23.5497C31.866 23.2066 32.3449 21.5482 32.4879 20.7619L31.6301 20.6547L31.8445 20.4403L32.9168 20.1186C33.1312 19.9399 33.603 19.5396 33.7745 19.368C33.9461 19.1965 33.846 18.7962 33.7745 18.6175C33.4529 17.724 33.3242 15.6582 35.3828 14.5431C37.4415 13.428 41.3872 14.0068 41.3872 14.0068C41.3872 14.0068 37.849 14.3285 36.3479 14.9718C35.147 15.4865 35.0612 16.9018 35.4901 16.7947L36.6695 15.7224L36.7767 16.4731L37.9562 15.5081C37.5273 16.3658 37.6988 17.4809 37.9562 16.3658C38.2778 14.972 40.315 14.4359 41.3872 14.3286C42.2377 14.2436 43.4255 14.4958 43.9339 14.6038L43.9339 14.6038C44.0665 14.632 44.1528 14.6503 44.175 14.6503C44.2008 14.6503 44.3812 14.706 44.6555 14.7906C45.5228 15.0582 47.3276 15.6151 48.1423 15.6151C49 15.6151 49.3217 15.2577 49.4289 15.079C49.5361 15.4007 49.3645 16.0656 48.2494 16.1514C47.2908 16.2251 46.6365 16.096 45.4844 15.8687C44.9612 15.7655 44.3353 15.642 43.5317 15.5081C40.9584 15.0792 40.3256 15.6047 39.7789 16.1514C39.4573 16.4731 39.3501 16.7947 39.4573 16.7947C39.4905 16.7947 39.5578 16.7885 39.6551 16.7796C40.2596 16.724 42.0212 16.5621 43.9606 17.1162C45.7619 17.6309 47.07 19.368 47.3916 20.1186L46.3194 19.7969C46.3194 19.9756 46.5694 20.7118 47.7134 21.5125C48.7856 22.2629 50.1795 22.3701 50.1795 22.3701C50.1795 22.3701 49.7506 23.1208 48.8928 23.0134C48.035 22.9061 47.4989 22.7991 47.4989 22.7991C47.4989 22.7991 48.1423 25.1578 47.7134 27.4095C47.2845 29.6611 43.9606 32.2346 42.3522 32.8779C41.0656 33.3926 40.1006 33.6642 39.7789 33.7357C42.0306 34.0572 42.674 34.8541 42.1378 34.8079C37.1627 34.379 34.6323 36.0588 33.989 36.9523C33.6674 37.399 36.6681 39.2962 38.814 39.0966C43.7462 38.6378 47.9278 33.0548 49.7506 28.5889C51.3602 24.6452 52.5383 16.6875 52.5383 13.8997L52.4842 13.8972C50.952 13.8243 47.3993 13.6554 45.2472 12.8275C42.4595 11.7552 39.4573 9.61084 38.9212 9.07467C38.7671 8.92051 38.5996 9.07469 38.5996 9.07469C37.9562 9.71793 35.8544 11.3513 33.1312 12.3986ZM35.0612 20.7616C34.9897 20.4042 35.0183 19.5608 35.7045 19.0461C36.3907 18.5314 37.0627 18.6887 37.3129 18.8316C37.3844 19.1176 37.3772 19.8181 36.7768 20.3328C36.1764 20.8474 35.3829 20.8331 35.0612 20.7616Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36315" x="-4" y="3" width="85.3127" height="45.0986" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36315"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36315" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36315">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-EVO" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36332)">
                    <g filter="url(#filter0_d_1701_36332)">
                        <path d="M23.4321 11.6882H39.5375V15.601H27.6177V21.3081H35.5531V25.2209H27.6177V31.6109H39.9067V35.5238H23.4321V11.6882Z" fill="white"></path>
                        <path d="M38.7466 18.5794H43.0321L46.7818 30.3893L50.2638 18.5794H54.4161L48.6567 35.5221H44.8055L38.7466 18.5794Z" fill="white"></path>
                        <path d="M53.76 27.0864C53.76 21.918 57.1088 18.2566 61.8966 18.2566C66.6512 18.2566 70 21.918 70 27.0864C70 32.1835 66.5846 35.8449 61.7968 35.8449C57.0406 35.8449 53.76 32.2549 53.76 27.0864ZM65.7795 27.1935V27.0508C65.7795 23.8209 64.3056 21.8823 61.895 21.8823C59.4512 21.8823 57.9773 23.8209 57.9773 27.0508V27.1935C57.9773 30.352 59.4163 32.2192 61.8285 32.2192C64.2723 32.2192 65.7795 30.3537 65.7795 27.1935Z" fill="white"></path>
                        <path d="M17.5317 38.6466C16.8185 38.6466 16.2416 38.0265 16.2416 37.2619C16.2432 36.4956 16.8201 35.8772 17.5317 35.8772C18.2465 35.8772 18.8218 36.4956 18.8233 37.2619C18.8218 38.0265 18.2449 38.6466 17.5317 38.6466ZM17.5317 35.5221C16.6378 35.5221 15.912 36.302 15.912 37.2619C15.912 38.2218 16.6378 39 17.5317 39C18.4271 39 19.153 38.2218 19.153 37.2619C19.153 36.302 18.4271 35.5221 17.5317 35.5221Z" fill="white"></path>
                        <path d="M14.885 22.776C16.1751 22.8202 17.5903 22.7828 19.153 22.6503C18.928 19.2777 17.9041 16.476 16.37 14.3047C16.4429 14.8993 16.4841 15.5042 16.4841 16.1209C16.4841 18.5353 15.9025 20.8035 14.885 22.776Z" fill="white"></path>
                        <path d="M14.3445 23.7326C12.0433 27.4466 8.10813 29.8949 3.63725 29.8949C3.31869 29.8949 3.00331 29.8779 2.69109 29.8542C1.54999 31.7299 0.813032 33.8129 0 35.7719C8.01938 40.3626 18.3748 35.5221 19.153 23.8345C17.3685 23.6833 15.7757 23.6561 14.3445 23.7326Z" fill="white"></path>
                        <path d="M4.17912e-08 10.7146C2.22989 16.0938 3.90033 22.4073 14.885 22.776C15.9025 20.8035 16.4841 18.5353 16.4841 16.1209C16.4841 15.5042 16.4429 14.8993 16.37 14.3047C12.5885 8.95786 5.7039 7.44912 4.17912e-08 10.7146Z" fill="white"></path>
                        <path d="M14.3445 23.7326C7.7341 24.0826 4.62937 26.6668 2.69109 29.8542C3.00331 29.8779 3.31869 29.8949 3.63725 29.8949C8.10813 29.8949 12.0433 27.4449 14.3445 23.7326Z" fill="white"></path>
                        <path d="M16.912 36.98H16.6695V36.8084H17.3574V36.98H17.1165V37.7156H16.912V36.98Z" fill="white"></path>
                        <path d="M17.443 36.8066H17.7663L17.9216 37.3859L18.0832 36.8066H18.3986V37.7138H18.1973V36.9816L17.9834 37.7138H17.8233L17.6173 36.985V37.7138H17.443V36.8066Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36332" x="-4" y="7" width="78" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36332"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36332" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36332">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-FISH" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36313)">
                    <g filter="url(#filter0_d_1701_36313)">
                        <path d="M10.8317 28.3509C10.5476 29.6294 9.47029 30.9552 8.96718 31.4584C9.4111 31.4584 10.3911 31.2703 11.7196 31.547C13.4065 31.8984 14.472 33.4708 16.6916 33.3227C19.3552 33.1451 19.8892 31.5413 22.3737 31.4584C25.0374 31.3695 26.77 32.7017 27.4346 32.9676C29.6542 33.8554 31.8686 32.7212 31.6961 32.6126C26.9019 29.5935 26.9906 25.6871 22.5514 19.6498C18.551 14.2093 11.6307 13.7902 9.4111 14.0565C8.20073 14.2017 7.85799 14.9324 8.34569 15.2107C8.96727 15.5654 10.0931 16.0093 10.8317 17.6967C11.4533 19.1168 11.098 21.2481 11.5419 22.935C11.869 24.178 13.8504 24.2668 13.8504 24.4443C13.8504 24.8936 12.0747 25.4506 11.2756 25.6873C11.246 26.0425 11.1158 27.0724 10.8317 28.3509Z" fill="white"></path>
                        <path d="M10.2991 33.0561C8.45234 33.0561 6.30374 34.3583 5.50467 34.9206C5.77103 35.4533 6.17532 36.3043 6.48131 36.0748C6.83645 35.8084 8.43458 34.9206 9.85514 34.743C10.743 34.632 11.4533 34.6542 12.2523 35.1869C13.3178 35.8972 15.5198 36.5187 16.4252 36.5187C17.0467 36.5187 19.0888 36.4299 20.3318 35.5421C21.8762 34.4389 24.0607 34.6542 24.5935 34.9206C25.4813 35.3645 26.9019 36.2346 28.3224 36.5187C29.743 36.8028 30.8084 36.3707 31.4299 36.2523L33.472 34.6542C33.9455 33.7664 34.6933 32.2248 33.2056 33.5C31.9626 34.5654 28.8551 35.3645 27.257 34.4766C25.657 33.5877 25.0374 33.1449 22.9953 33.1449C20.9533 33.1449 19.6215 34.2103 18.5561 34.6542C17.6682 35.0242 16.9579 35.2757 15.1822 34.743C13.4065 34.2103 12.6075 33.0561 10.2991 33.0561Z" fill="white"></path>
                        <path d="M10.2991 36.1161C8.45234 36.1161 7.54673 36.8855 6.74766 37.4478C7.01402 37.9805 7.41831 38.8315 7.7243 38.602C8.07944 38.3357 8.43458 37.9805 9.85514 37.803C10.743 37.692 11.7196 37.6729 12.5187 38.2056C13.5841 38.9159 15.1822 39.5787 16.4252 39.5787C17.0467 39.5787 19.0888 39.4899 20.3318 38.602C21.8762 37.4989 24.0607 37.7142 24.5935 37.9805C25.4813 38.4245 26.9019 39.2946 28.3224 39.5787C29.743 39.8628 29.0327 39.4307 29.6542 39.3123L31.6963 37.7142C32.1698 36.8263 32.9176 35.2848 31.4299 36.56C30.1869 37.6254 28.8551 38.4245 27.257 37.5366C25.657 36.6477 25.0374 36.2048 22.9953 36.2048C20.9533 36.2048 19.6215 37.2703 18.5561 37.7142C17.6682 38.0841 16.9579 38.3357 15.1822 37.803C13.4065 37.2703 12.6075 36.1161 10.2991 36.1161Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19 41.2243C28.5127 41.2243 36.2243 33.5127 36.2243 24C36.2243 14.4873 28.5127 6.7757 19 6.7757C9.48728 6.7757 1.7757 14.4873 1.7757 24C1.7757 33.5127 9.48728 41.2243 19 41.2243ZM19 43C29.4934 43 38 34.4934 38 24C38 13.5066 29.4934 5 19 5C8.50659 5 0 13.5066 0 24C0 34.4934 8.50659 43 19 43Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36313" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36313"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36313" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36313">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-HB" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36287)">
                    <g filter="url(#filter0_d_1701_36287)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M13.8039 0C13.1109 0 12.549 0.561839 12.549 1.2549V1.88235C12.549 2.57542 13.1109 3.13725 13.8039 3.13725C14.497 3.13725 15.0588 2.57542 15.0588 1.88235V1.2549C15.0588 0.561839 14.497 0 13.8039 0ZM21.0196 9.09804C21.0196 8.40498 21.5814 7.84314 22.2745 7.84314C22.9676 7.84314 23.5294 8.40498 23.5294 9.09804V9.72549C23.5294 10.4186 22.9676 10.9804 22.2745 10.9804C21.5814 10.9804 21.0196 10.4186 21.0196 9.72549V9.09804ZM22.2745 11.6078C21.5814 11.6078 21.0196 12.1697 21.0196 12.8627V15.3233L21.0129 15.32C20.962 15.7039 20.6333 16 20.2353 16C19.8021 16 19.451 15.6489 19.451 15.2157V14.5544V9.41177C19.451 8.7187 18.8891 8.15686 18.1961 8.15686C17.503 8.15686 16.9412 8.7187 16.9412 9.41177V13.3241V13.9608C16.9412 14.394 16.5901 14.7451 16.1569 14.7451C15.7237 14.7451 15.3725 14.394 15.3725 13.9608V13.1703V12.3922C15.3725 11.6125 14.7405 10.9804 13.9608 10.9804C13.1811 10.9804 12.549 11.6125 12.549 12.3922V14.5544V14.5882V15.8431C12.549 16.2764 12.1979 16.6275 11.7647 16.6275C11.3315 16.6275 10.9804 16.2764 10.9804 15.8431V15.3234V10.9804C10.9804 10.2873 10.4186 9.72549 9.72549 9.72549C9.03243 9.72549 8.47059 10.2873 8.47059 10.9804V16.5536V17.5686V18.0392C8.47059 18.4724 8.11948 18.8235 7.68627 18.8235C7.25306 18.8235 6.90196 18.4724 6.90196 18.0392V17.3226V15.6863C6.90196 14.9932 6.34012 14.4314 5.64706 14.4314C4.954 14.4314 4.39216 14.9932 4.39216 15.6863V18.5529V19.1373V21.1765C4.39216 21.6097 4.04105 21.9608 3.60784 21.9608C3.17463 21.9608 2.82353 21.6097 2.82353 21.1765V19.3218V18.9804C2.82353 18.2007 2.19146 17.5686 1.41176 17.5686C0.632069 17.5686 0 18.2007 0 18.9804V20.7059V22.1176V38.0718C0 38.7805 0.375083 39.4363 0.98596 39.7957L12.9579 46.838C14.8356 47.9425 17.1644 47.9425 19.0421 46.838L31.014 39.7957C31.6249 39.4363 32 38.7805 32 38.0718V22.1176V20.7059V18.9804C32 18.2007 31.3679 17.5686 30.5882 17.5686C29.8085 17.5686 29.1765 18.2007 29.1765 18.9804V19.3217V20.2353C29.1765 20.6685 28.8254 21.0196 28.3922 21.0196C27.9589 21.0196 27.6078 20.6685 27.6078 20.2353V19.451V18.5529V16C27.6078 15.3069 27.046 14.7451 26.3529 14.7451C25.6599 14.7451 25.098 15.3069 25.098 16V17.3225L25.0695 17.3085C24.9776 17.6394 24.674 17.8824 24.3137 17.8824C23.8805 17.8824 23.5294 17.5313 23.5294 17.098V16.5536L23.5085 16.5433C23.5222 16.4689 23.5294 16.3922 23.5294 16.3137V12.8627C23.5294 12.1697 22.9676 11.6078 22.2745 11.6078ZM13.9608 4.39216C13.1811 4.39216 12.549 5.02423 12.549 5.80392V8.31373C12.549 9.09342 13.1811 9.72549 13.9608 9.72549C14.7405 9.72549 15.3725 9.09342 15.3725 8.31373V5.80392C15.3725 5.02423 14.7405 4.39216 13.9608 4.39216ZM16.6275 2.66667C16.6275 1.88697 17.2595 1.2549 18.0392 1.2549C18.8189 1.2549 19.451 1.88697 19.451 2.66667V5.4902C19.451 6.26989 18.8189 6.90196 18.0392 6.90196C17.2595 6.90196 16.6275 6.26989 16.6275 5.4902V2.66667ZM8.47064 24.1567H14.1177V27.6077H18.1961V24.1567H23.8432V35.9214H18.353V32.6273H14.1177V35.9214H8.47064V24.1567Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36287" x="-4" y="-2" width="40" height="55.6663" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36287"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36287" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36287">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-IM" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36309)">
                    <g filter="url(#filter0_d_1701_36309)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19 43C29.4934 43 38 34.4934 38 24C38 13.5066 29.4934 5 19 5C8.50659 5 0 13.5066 0 24C0 34.4934 8.50659 43 19 43ZM5.05648 16.8302C4.24375 16.8302 3.58491 17.4988 3.58491 18.3236V29.2757C3.58491 30.1005 4.24375 30.7691 5.05648 30.7691C5.86921 30.7691 6.52806 30.1005 6.52806 29.2757V18.3236C6.52806 17.4988 5.86921 16.8302 5.05648 16.8302ZM12.4144 16.8302C11.6016 16.8302 10.9428 17.4988 10.9428 18.3236V29.2757C10.9428 30.1005 11.6016 30.7691 12.4144 30.7691C13.2271 30.7691 13.8859 30.1005 13.8859 29.2757V22.004L21.5702 30.3386C22.1085 30.9226 23.0047 30.972 23.6022 30.4507C24.2364 29.8974 24.2887 28.9194 23.7174 28.2997L13.6472 17.3771C13.492 17.2088 13.3072 17.0849 13.1083 17.0063C12.9016 16.8939 12.6653 16.8302 12.4144 16.8302ZM21.9161 17.2651C21.282 17.8184 21.2297 18.7963 21.801 19.4161L31.8712 30.3386C32.4096 30.9226 33.3058 30.972 33.9033 30.4507C34.5374 29.8974 34.5898 28.9194 34.0184 28.2997L23.9482 17.3771C23.4099 16.7932 22.5137 16.7438 21.9161 17.2651Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36309" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36309"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36309" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36309">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-JDB" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36297)">
                    <g filter="url(#filter0_d_1701_36297)">
                        <path d="M21.384 8H11.5708L14.9396 12.7454L10.2521 31.8799C10.1056 32.3391 9.57897 33.9005 8.05573 35.2476C6.53248 36.5946 4.10108 35.9109 3.07579 35.4007C2.34288 36.4722 0 39.8399 0 39.8399C0 39.8399 3.88371 42.11 9.0809 40.2991C12.1561 39.2276 14.646 35.4007 15.2325 32.7984L21.384 8Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M21.8229 8.15308L51.702 8C53.7525 8 58 9.98999 58 14.5823C58 18.103 56.0959 20.2461 55.0707 20.8584L55.0773 20.8661C55.5202 21.3804 56.5353 22.559 56.5353 25.9099C56.5353 30.043 51.9949 33.8699 47.4544 33.8699H19.4794L24.8987 12.8984L21.8229 8.15308ZM29.8785 12.7454L25.7775 28.8184L25.8117 28.8214C27.5785 28.9753 30.9227 29.2667 33.8331 27.5938C36.7624 25.9099 39.6917 21.7769 39.5453 17.6438C39.3988 13.5107 36.1766 12.7454 34.2725 12.7454H29.8785ZM49.4134 23.3076H42.914C42.3281 25.7568 40.2776 28.0529 39.2523 28.9714C41.216 28.9714 44.4706 28.8952 46.3863 28.8503L46.3873 28.8503C47.186 28.8316 47.7517 28.8183 47.8938 28.8183C49.0656 28.8184 51.8582 27.8999 51.702 25.4507C51.5848 23.6138 50.2373 23.3077 49.4134 23.3076ZM44.0857 18.4092H49.9993C50.9696 18.4092 53.0202 17.6438 53.0202 15.5007C53.0202 13.3577 51.7477 12.7454 50.3838 12.7454C50.1506 12.7454 49.0853 12.7454 47.7325 12.7454C46.2818 12.7454 44.5005 12.7454 43.0605 12.7454C43.6463 13.8169 44.2322 16.2661 44.0857 18.4092Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36297" x="-4" y="6" width="66" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36297"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36297" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36297">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-JILI" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36358)">
                    <g filter="url(#filter0_d_1701_36358)">
                        <path d="M12.0619 35.9586V11.022C12.0619 10.3785 11.4242 10.2176 10.8505 10.2176C10.1774 10.2176 8.83131 10.2176 8.83134 9.01101C8.83138 7.4022 9.63895 7.4022 10.0428 7.4022H24.5805C24.9843 7.4022 25.3882 7.80441 25.3881 9.01101C25.3881 9.81542 24.5805 10.2176 24.1767 10.2176C22.9652 10.2176 22.9652 11.022 22.9652 11.022V33.5454C22.9652 35.9586 21.3499 37.5674 18.9269 39.1762C15.8621 41.2112 12.1965 41.1872 10.4466 40.785V37.5674C10.4466 37.5674 12.0619 36.763 12.0619 35.9586Z" fill="white"></path>
                        <path d="M1.15873 37.1652C2.77399 39.5784 6.81222 40.785 8.42751 40.785V37.9696C6.94683 38.1037 6.55006 37.2522 6.81218 37.1652C10.4466 35.9586 10.8504 33.1432 10.4466 31.5344C10.1528 30.3638 8.02376 26.3057 3.58166 27.9145C-0.505854 29.3949 -0.76819 34.2864 1.15873 37.1652Z" fill="white"></path>
                        <path d="M27.8111 8.60881C27.8111 7.72029 28.5343 7 29.4264 7H43.1564C44.0485 7 44.7717 7.72029 44.7717 8.60881V8.82112C44.7717 9.43049 44.426 9.98756 43.8788 10.2601L42.3487 11.022V36.763L43.6672 37.2007C44.3268 37.4197 44.7717 38.0345 44.7717 38.727V39.1762C44.7717 40.0647 44.0485 40.785 43.1564 40.785H29.4264C28.5343 40.785 27.8111 40.0647 27.8111 39.1762V38.727C27.8111 38.0345 28.256 37.4197 28.9156 37.2007L30.234 36.763V11.022L28.704 10.2601C28.1568 9.98756 27.8111 9.43049 27.8111 8.82112V8.60881Z" fill="white"></path>
                        <path d="M73.0394 8.60881C73.0394 7.72029 73.7626 7 74.6547 7H88.3847C89.2768 7 90 7.72029 90 8.60881V8.82112C90 9.43049 89.6543 9.98756 89.1071 10.2601L87.5771 11.022V36.763L88.8955 37.2007C89.5551 37.4197 90 38.0345 90 38.727V39.1762C90 40.0647 89.2768 40.785 88.3847 40.785H74.6547C73.7626 40.785 73.0394 40.0647 73.0394 39.1762V38.727C73.0394 38.0345 73.4843 37.4197 74.1439 37.2007L75.4623 36.763V11.022L73.9323 10.2601C73.3851 9.98756 73.0394 9.43049 73.0394 8.82112V8.60881Z" fill="white"></path>
                        <path d="M47.1946 8.60881C47.1946 7.72029 47.9178 7 48.8099 7H62.54C63.4321 7 64.1553 7.72029 64.1553 8.60881V8.82112C64.1553 9.43049 63.8096 9.98756 63.2623 10.2601L61.7323 11.022V40.785H48.8099C47.9178 40.785 47.1946 40.0647 47.1946 39.1762V38.727C47.1946 38.0345 47.6395 37.4197 48.2991 37.2007L49.6176 36.763V11.022L48.0875 10.2601C47.5403 9.98756 47.1946 9.43049 47.1946 8.82112V8.60881Z" fill="white"></path>
                        <path d="M66.982 33.9476C68.3003 32.1094 69.1022 30.0997 69.5 28.9261C69.6997 28.3369 70.2468 27.9145 70.8711 27.9145C71.6226 27.9145 72.2317 28.5213 72.2317 29.2697V40.785H62.54V37.5674C62.54 37.5674 64.9629 36.763 66.982 33.9476Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36358" x="-4" y="5" width="98" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36358"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36358" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36358">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-MG" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36291)">
                    <g filter="url(#filter0_d_1701_36291)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19 43C29.4934 43 38 34.4934 38 24C38 13.5066 29.4934 5 19 5C8.50659 5 0 13.5066 0 24C0 25.9872 0.305066 27.9031 0.87092 29.7035C0.877451 29.6924 0.884183 29.6817 0.891121 29.6713C3.11952 26.9737 6.93368 21.5391 8.28001 19.1157C10.0393 15.9491 11.7985 12.196 12.5022 10.6713C12.7368 10.2021 13.4874 8.9823 14.6133 9.26378C15.6789 9.53018 15.673 11.0059 15.6693 11.9277C15.6691 11.9799 15.6689 12.0304 15.6689 12.0787C15.6689 13.4079 15.6272 15.0013 15.5824 16.7097C15.4022 23.5937 15.173 32.344 17.4282 33.1898C18.9014 33.7424 23.7603 25.097 25.8714 18.4118C25.8907 18.354 25.9098 18.2961 25.9288 18.2383C26.2568 17.2437 26.5677 16.3009 27.6319 16.3009C28.5435 16.3009 28.6701 17.2235 28.872 18.6953C28.9195 19.0412 28.9711 19.4175 29.0381 19.8193C29.0618 19.9613 29.087 20.1176 29.1143 20.2865C29.4926 22.6277 30.2595 27.3738 32.5566 29.6711C33.8004 30.915 33.964 32.1341 31.1492 30.023C30.68 29.6711 29.0381 28.2637 28.3344 25.4489C27.6307 22.6341 27.0689 22.9857 26.9282 23.6896C26.5763 25.4489 21.6504 36.3565 17.78 36.3565C12.2436 36.3565 12.6225 27.2147 12.8055 22.7987C12.8318 22.1631 12.8541 21.6253 12.8541 21.2268C12.8541 18.0602 11.0948 21.2268 10.0393 23.3379C9.9062 23.6041 9.72405 23.9804 9.50364 24.4357C8.07929 27.3782 5.05685 33.6219 3.35213 34.7801C6.77962 39.7458 12.5098 43 19 43Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36291" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36291"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36291" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36291">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-PG" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36295)">
                    <g filter="url(#filter0_d_1701_36295)">
                        <path d="M51.4286 11H37.9592V13.3636H35.5102V16.9091H31.8367V32.2727H35.5102V34.6364H37.9592V37H55.102V34.6364H57.551V32.2727H60V21.6364H46.5306V26.3636H55.102V28.7273H53.8776V29.9091H51.4286V32.2727H41.6327V29.9091H40.4082V28.7273H37.9592V19.2727H40.4082V15.7273H52.6531V19.2727H57.551V14.5455H56.3265V13.3636H51.4286V11Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 13.3636H2.44898V11H25.7143V14.5455H24.4898V16.9091H28.1633V26.3636H24.4898V29.9091H19.5918V32.2727H6.12245V37H0V13.3636ZM4.89796 15.7273H19.5918V19.2727H22.0408V24H19.5918V27.5455H4.89796V15.7273Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36295" x="-4" y="9" width="68" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36295"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36295" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36295">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-PP" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36368)">
                    <g filter="url(#filter0_d_1701_36368)">
                        <path d="M21.7631 4C22.1729 4.03482 22.583 4.0673 22.9926 4.10493C26.3918 4.41645 29.6048 5.31196 32.5023 7.17215C34.2847 8.3164 35.768 9.76905 36.9151 11.5566C37.1747 11.9614 37.4147 11.9957 37.7773 11.6747C39.5844 10.0745 41.6014 8.82404 43.8827 8.01722C45.9848 7.27364 48.149 6.95448 50.3744 7.13061C52.7872 7.32158 55.0559 8.00411 57.1647 9.20005C57.4384 9.35526 57.7126 9.51374 57.9654 9.69956C58.0884 9.78997 58.256 9.90474 58.1643 10.1168C58.0705 10.334 57.8516 10.4625 57.6219 10.4516C56.5713 10.4019 55.52 10.304 54.47 10.3165C49.646 10.3743 45.2165 11.7549 41.1684 14.3707C39.8524 15.2211 38.6255 16.1869 37.4677 17.242C37.3751 17.3265 37.2578 17.397 37.1394 17.4369C36.9928 17.4862 36.8581 17.4831 36.7419 17.3185C34.4979 14.1399 31.4797 12.0068 27.8193 10.7561C25.7711 10.0562 23.6578 9.7831 21.5043 9.98266C17.6768 10.3373 14.2458 11.6773 11.3403 14.2413C9.04354 16.2681 7.64555 18.8356 6.88277 21.7725C6.38418 23.6925 6.22741 25.6476 6.35857 27.6232C6.49317 29.6505 6.96381 31.6153 7.52828 33.5593C8.09369 35.5068 8.66972 37.4513 9.22858 39.4007C9.34179 39.7954 9.41424 40.2039 9.47514 40.6105C9.50309 40.7971 9.48263 41.0006 9.43938 41.1859C9.36021 41.5252 9.02606 41.7443 8.66879 41.6378C8.29575 41.5266 7.90585 41.3869 7.59699 41.1595C5.95196 39.9483 4.56381 38.4838 3.43814 36.7761C1.91679 34.4683 0.965374 31.9327 0.429005 29.2307C0.00209638 27.0799 -0.0475593 24.9085 0.0309832 22.7265C0.121861 20.199 0.644021 17.7699 1.6963 15.4683C3.3254 11.905 5.82158 9.10526 9.17799 7.07815C11.3027 5.79492 13.5821 4.89582 16.0177 4.42815C17.0152 4.23656 18.0371 4.17176 19.0476 4.04809C19.1431 4.03638 19.2374 4.01655 19.3323 4.00047C20.1427 4 20.9528 4 21.7631 4Z" fill="white"></path>
                        <path d="M18.4086 43.2808C18.3293 43.2699 18.2501 43.254 18.1703 43.249C17.8719 43.2309 17.7631 43.1358 17.7417 42.8371C17.7265 42.6273 17.7314 42.4161 17.7314 42.2054C17.7317 34.7932 17.7337 27.381 17.73 19.9688C17.7298 19.5961 17.888 19.3182 18.1852 19.1384C18.4732 18.964 18.7859 18.8218 19.1021 18.7039C20.5419 18.1674 22.0334 17.8562 23.5618 17.7134C24.756 17.6019 25.9512 17.5151 27.1501 17.624C29.2245 17.8123 31.112 18.4328 32.5558 20.0319C33.5356 21.117 34.1018 22.4144 34.4044 23.8282C34.9038 26.1603 34.8977 28.4894 34.2391 30.7919C33.7854 32.3776 33.0189 33.7904 31.7332 34.8671C30.5497 35.8582 29.1705 36.3875 27.636 36.5168C26.0756 36.6484 24.5327 36.5141 23.0025 36.2006C22.9716 36.1942 22.9395 36.1929 22.8855 36.1865C22.879 36.2629 22.868 36.3321 22.8677 36.4014C22.8609 38.5075 22.8532 40.6137 22.852 42.7199C22.8518 42.9412 22.8078 43.1314 22.6377 43.2808C21.2283 43.2808 19.8185 43.2808 18.4086 43.2808ZM22.854 27.06C22.8566 27.06 22.8593 27.06 22.8619 27.06C22.8619 28.534 22.8668 30.0081 22.8565 31.482C22.8551 31.6867 22.9211 31.7629 23.1121 31.8033C23.9689 31.9843 24.8296 32.1192 25.709 32.1055C27.3303 32.08 28.4316 31.3347 28.977 29.8041C29.5638 28.1576 29.5538 26.4616 29.1702 24.7799C28.7896 23.1121 27.8287 22.2246 25.8539 22.0681C24.9056 21.9928 23.985 22.1541 23.0671 22.362C22.9013 22.3994 22.8493 22.4675 22.8501 22.6382C22.8569 24.1121 22.854 25.5861 22.854 27.06Z" fill="white"></path>
                        <path d="M37.2225 43.2808C37.0072 43.1632 36.9257 42.9752 36.9357 42.7382C36.9383 42.6735 36.9358 42.6086 36.9358 42.5438C36.9355 35.0266 36.9368 27.5095 36.9318 19.9924C36.9315 19.4777 37.2089 19.1663 37.6361 18.9901C38.2475 18.7376 38.8672 18.4924 39.5031 18.3142C41.4796 17.7605 43.505 17.5508 45.5524 17.5833C46.9039 17.6047 48.2264 17.8199 49.4731 18.3786C51.4103 19.2466 52.6203 20.76 53.3045 22.7295C53.9015 24.4474 54.0169 26.2297 53.9207 28.0246C53.8275 29.764 53.4187 31.4319 52.5272 32.9492C51.3247 34.9959 49.5359 36.1654 47.1798 36.4705C45.5803 36.6777 43.999 36.5324 42.4236 36.2457C42.315 36.2258 42.205 36.2121 42.062 36.1902C42.062 36.3122 42.062 36.4071 42.062 36.5019C42.062 38.4784 42.0625 40.4548 42.0611 42.4313C42.0609 42.5767 42.0756 42.7316 42.0317 42.8651C41.9827 43.0139 41.8746 43.1432 41.7922 43.2809C40.269 43.2808 38.7457 43.2808 37.2225 43.2808ZM42.0489 27.0845C42.05 27.0845 42.0512 27.0845 42.0523 27.0845C42.0523 28.5346 42.0584 29.9848 42.0464 31.4348C42.0444 31.6787 42.1221 31.7811 42.357 31.8147C42.6766 31.8604 42.9909 31.9435 43.3106 31.9889C44.0816 32.0987 44.8574 32.1807 45.6316 32.039C46.9516 31.7975 47.7919 30.9949 48.2135 29.7437C48.7391 28.1837 48.7467 26.5824 48.4279 24.995C48.0395 23.0618 46.9855 22.2065 45.0124 22.0645C44.1135 21.9998 43.2409 22.1488 42.3684 22.3408C42.107 22.3985 42.0406 22.5031 42.0428 22.7582C42.0553 24.2002 42.0489 25.6424 42.0489 27.0845Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36368" x="-4" y="2" width="66.1907" height="47.281" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36368"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36368" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36368">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-SABA" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36307)">
                    <g filter="url(#filter0_d_1701_36307)">
                        <path d="M17.6816 24.4392H15.8146V26.1964H17.6816C17.915 26.1737 18.1403 26.0986 18.3406 25.9768C18.5603 25.7571 18.5603 25.6473 18.5603 25.3178C18.5375 25.0844 18.4624 24.8592 18.3406 24.6588C18.2538 24.5758 18.1497 24.5131 18.0358 24.4751C17.9218 24.4371 17.8009 24.4248 17.6816 24.4392Z" fill="white"></path>
                        <path d="M5.16186 31.1387C5.71637 32.345 6.33974 33.5184 7.02891 34.6532C7.13873 34.8728 7.24856 34.9827 7.35839 35.2023C7.00105 34.2715 6.7075 33.3175 6.47978 32.3468C6.1503 32.0173 5.60116 31.578 5.16186 31.1387Z" fill="white"></path>
                        <path d="M6.36995 31.3584C5.93169 29.4491 5.71063 27.4965 5.71099 25.5376C5.71099 24.4393 5.82082 23.341 5.93064 22.2428C4.62388 20.2282 3.58983 18.0493 2.8555 15.763C2.32927 20.4859 2.93151 25.2662 4.61273 29.711C5.16186 30.2601 5.71099 30.8092 6.36995 31.3584Z" fill="white"></path>
                        <path d="M6.36995 23.1214C6.27273 23.9597 6.23603 24.8038 6.26012 25.6474C6.29049 27.8282 6.54814 29.9998 7.02891 32.1272C10.3159 34.8226 14.2558 36.6031 18.4509 37.289C19.574 37.4976 20.7132 37.6078 21.8555 37.6185C24.0893 37.0351 26.2326 36.1482 28.2254 34.9827C23.914 34.8271 19.6972 33.6753 15.9054 31.6175C12.1136 29.5596 8.84986 26.6516 6.36995 23.1214Z" fill="white"></path>
                        <path d="M7.35839 33.0058C7.71804 34.2919 8.196 35.5419 8.78613 36.7399C11.0445 37.5646 13.4139 38.0459 15.815 38.1676C17.1003 38.2098 18.3868 38.1363 19.659 37.948C19.2489 37.9238 18.8432 37.8501 18.4509 37.7283C14.4247 37.0784 10.6174 35.4575 7.35839 33.0058Z" fill="white"></path>
                        <path d="M19 5C15.2422 5 11.5687 6.11433 8.44417 8.20208C5.31964 10.2898 2.88436 13.2572 1.4463 16.729C0.00823312 20.2008 -0.368031 24.0211 0.365088 27.7067C1.09821 31.3923 2.90778 34.7778 5.56498 37.435C8.22218 40.0922 11.6076 41.9018 15.2933 42.6349C18.9789 43.368 22.7992 42.9918 26.271 41.5537C29.7428 40.1156 32.7102 37.6804 34.7979 34.5558C36.8857 31.4313 38 27.7578 38 24C37.9971 18.9618 35.9944 14.1307 32.4318 10.5682C28.8693 7.00562 24.0382 5.00291 19 5ZM32.948 28.3931H30.6416L28.0058 23.5607L22.5145 25.5376L26.9075 17.5202L32.948 28.3931ZM26.2486 9.72254L31.4104 19.1676H29.1041L26.7977 14.9942L22.6243 16.3121L26.2486 9.72254ZM21.7457 17.7399L24.6012 16.8613L23.3931 19.1676H21.0867L21.7457 17.7399ZM21.6358 26.9653L24.4913 26.0867L23.2832 28.3931H20.9769L21.6358 26.9653ZM20.6474 21.5838C20.6654 22.2995 20.4323 22.9989 19.9884 23.5607C20.2323 23.8664 20.4136 24.2171 20.5219 24.5929C20.6302 24.9687 20.6633 25.3621 20.6195 25.7508C20.5756 26.1394 20.4556 26.5155 20.2663 26.8577C20.077 27.1999 19.8221 27.5014 19.5162 27.7451C18.9968 28.1642 18.3495 28.3929 17.6821 28.3931H13.6185V20.9249H15.7052V22.4624H17.6821C17.9155 22.4397 18.1407 22.3646 18.341 22.2428C18.5607 22.0231 18.5607 21.9133 18.5607 21.5838C18.5607 21.3462 18.4836 21.115 18.341 20.9249C19.0453 20.7203 19.6872 20.3428 20.2081 19.8266C20.4845 20.3719 20.6347 20.9725 20.6474 21.5838ZM19.4393 9.61272V11.8092H16.0347C15.8013 11.832 15.5761 11.9071 15.3757 12.0289C15.1561 12.2486 15.1561 12.3584 15.1561 12.6879C15.1788 12.9212 15.2539 13.1465 15.3757 13.3468C15.5954 13.5665 15.7052 13.5665 16.0347 13.5665H17.133C17.9487 13.5773 18.7313 13.8904 19.3295 14.4451C19.6137 14.7351 19.8377 15.0785 19.9885 15.4555C20.1393 15.8325 20.2139 16.2356 20.2081 16.6416C20.1972 17.4573 19.8842 18.24 19.3295 18.8381C19.0395 19.1224 18.6961 19.3463 18.3191 19.4971C17.9421 19.6479 17.539 19.7226 17.133 19.7168H13.7283V17.5202H17.133C17.3663 17.4975 17.5916 17.4224 17.7919 17.3006C18.0116 17.0809 18.0116 16.9711 18.0116 16.6416C17.9888 16.4082 17.9137 16.183 17.7919 15.9827C17.7051 15.8996 17.601 15.8369 17.4871 15.7989C17.3731 15.7609 17.2522 15.7486 17.133 15.763H16.0347C15.219 15.7522 14.4363 15.4391 13.8382 14.8844C13.5539 14.5944 13.33 14.251 13.1792 13.874C13.0284 13.497 12.9537 13.0939 12.9595 12.6879C12.9704 11.8722 13.2835 11.0895 13.8382 10.4913C14.1281 10.2071 14.4715 9.98315 14.8485 9.83234C15.2256 9.68154 15.6287 9.6069 16.0347 9.61272H19.4393ZM34.2659 34.4335C34.0462 34.763 33.7168 35.2023 33.4971 35.5318C30.0413 37.2235 26.252 38.1239 22.4046 38.1676H22.0751C20.2776 38.6056 18.434 38.8268 16.5838 38.8266H15.7052C13.4946 38.7727 11.3069 38.3648 9.22544 37.6185C10.5349 39.2029 12.0492 40.6063 13.7283 41.7919C13.0694 41.5722 12.5202 41.3526 11.9711 41.1329C11.3336 40.6417 10.7452 40.09 10.2139 39.4855C10.4436 39.8695 10.7006 40.2365 10.9827 40.5838C7.85078 39.052 5.19362 36.6985 3.29481 33.7746C4.57944 34.7513 5.98152 35.563 7.46822 36.1907C7.13874 35.7514 6.91908 35.422 6.5896 34.9827C5.57714 33.4281 4.73051 31.7716 4.06359 30.0405C3.08764 28.9844 2.20568 27.8452 1.42775 26.6358C1.07387 26.1607 0.778853 25.6444 0.549141 25.0983V23.8902C0.560824 20.5618 1.47152 17.2985 3.18498 14.4451C3.83287 16.9176 4.83144 19.2846 6.1503 21.474C7.01548 15.5682 9.88493 10.1396 14.2775 6.09827L15.4856 5.76879C10.6554 10.0125 7.53275 15.8674 6.69943 22.2428C9.03014 25.9592 12.2708 29.0192 16.1145 31.1333C19.9583 33.2474 24.278 34.3455 28.6647 34.3237H29.2139C31.4033 32.8306 33.2571 30.897 34.6565 28.6466C36.0559 26.3961 36.9703 23.8784 37.341 21.2543C37.4379 22.1661 37.4746 23.0834 37.4509 24C37.4384 27.6812 36.3296 31.2753 34.2659 34.3237C32.64 34.6654 30.9849 34.8493 29.3237 34.8728C27.7341 35.9873 25.9967 36.8744 24.1619 37.5087C27.722 37.2766 31.1801 36.2241 34.2659 34.4335Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36307" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36307"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36307" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36307">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-SEXY" viewBox="0 0 90 47" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                <g filter="url(#filter0_d_1668_26851)">
                    <rect width="88" height="44" fill="url(#pattern0)" shape-rendering="crispEdges"></rect>
                </g>
                <defs>
                    <filter id="filter0_d_1668_26851" x="-2" y="-1" width="92" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="1"></feoffset>
                        <fegaussianblur stdDeviation="1"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.966667 0 0 0 0 0.433319 0 0 0 0 0.39875 0 0 0 1 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1668_26851"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1668_26851" result="shape"></feblend>
                    </filter>
                    <pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1">
                        <use xlink:href="#image0_1668_26851" transform="matrix(0.00359712 0 0 0.00719424 0 -0.00359712)"></use>
                    </pattern>
                    <image id="image0_1668_26851" width="278" height="140" xlink:href="data:image/png;base64,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********************************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"></image>
                </defs>
            </symbol>
            <symbol id="icon-SPRIBE" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36348)">
                    <g filter="url(#filter0_d_1701_36348)">
                        <path d="M88.9986 38.4387C88.9986 39.8392 88.9986 41.0066 88.9986 42.4068C59.3437 42.4068 29.7699 42.4068 0 42.4068C0 41.1437 0 39.908 0 38.4387C29.5434 38.4387 59.1199 38.4387 88.9986 38.4387Z" fill="white"></path>
                        <path d="M58.9536 6.12696C61.5043 6.12707 69.0177 5.10529 71.5286 9.48728C73.5434 13.0033 72.7753 17.1771 69.9749 20.5731C73.9505 23.911 73.4855 28.4765 72.2351 32.6622C71.7345 34.3382 69.1507 35.9324 67.2508 36.4063C64.6915 37.0448 58.9534 36.5648 58.9534 36.5648C58.9536 26.5903 58.9536 16.6639 58.9536 6.12696ZM63.5185 22.6097C63.5185 26.4507 63.5185 29.2182 63.5185 32.6671C64.3386 32.6671 67.173 32.7698 67.6321 31.342C68.0911 29.9142 68.5902 26.5342 67.6145 24.4212C66.6387 22.3082 65.4724 21.9993 63.5185 22.6097ZM63.4579 19.1808C64.0552 19.1808 66.574 18.9004 67.2987 17.1903C68.0233 15.4803 68.5902 13.7797 67.2319 11.7964C65.8736 9.81306 63.4579 10.5573 63.4579 10.5573C63.4579 13.5031 63.4579 15.7801 63.4579 19.1808Z" fill="white"></path>
                        <path d="M33.4451 6.12817C38.057 7.35181 43.4723 4.04561 46.4584 9.82963C48.3372 13.4689 47.404 18.7172 44.5945 21.6663C49.0378 25.7231 46.6139 31.1697 47.6168 36.1723C46.035 36.1723 44.6551 36.1723 42.4753 36.1723C42.4753 33.4837 42.417 30.8311 42.4909 28.1823C42.5729 25.2428 41.3603 23.9027 38.0945 24.4924C38.0945 28.274 38.0945 32.0782 38.0945 36.1216C36.4314 36.1216 35.0534 36.1216 33.4451 36.1216C33.4451 26.3874 33.4451 16.5837 33.4451 6.12817ZM38.0061 19.857C42.381 19.7737 42.8493 18.9553 42.4806 13.6566C42.2702 10.6345 40.5454 10.5294 38.1424 11.0081C38.0943 11.8819 38.0195 12.6843 38.0123 13.4875C37.9941 15.5302 38.0061 17.5729 38.0061 19.857Z" fill="white"></path>
                        <path d="M17.0041 6.3972C27.1165 5.46243 30.2812 5.6613 30.445 12.8383C30.6088 20.0154 30.2192 25.1058 21.4941 25.3752C21.4941 27.1063 21.4941 28.8549 21.4941 30.6036C21.4941 32.3734 21.4941 34.1433 21.4941 36.1712C19.9638 36.1712 18.606 36.1712 17.0039 36.1712C17.0041 26.3736 17.0041 16.4941 17.0041 6.3972ZM21.6038 20.5759C25.2913 20.7777 25.765 20.3379 25.8194 16.8991C25.8327 16.0664 25.8316 15.233 25.8203 14.4002C25.77 10.7333 25.2819 10.3359 21.6038 11.0603C21.6038 14.1334 21.6038 17.2282 21.6038 20.5759Z" fill="white"></path>
                        <path d="M14.1458 14.3313C12.6198 14.3313 10.9974 14.3313 10.2037 14.3313C9.89797 13.4975 9.27634 11.3039 8.50305 10.6631C7.72975 10.0224 5.6687 10.3797 5.10183 11.7969C4.53496 13.214 5.37792 14.881 6.13088 16.1875C7.29331 18.2044 8.98578 19.2189 10.7285 21.6017C12.4711 23.9846 14.025 26.3094 14.0854 29.3601C14.1458 32.4107 11.6731 36.6007 7.83117 36.733C3.42707 36.8847 0.283707 34.2112 0.0119895 30.0328C-0.0401696 29.2291 0.0913428 28.4136 0.14796 27.4353C1.70805 27.4353 3.12705 27.4353 4.72258 27.4353C4.72258 27.9527 4.53496 29.6533 4.81839 30.5036C5.38526 31.9208 5.95213 32.2042 7.2231 32.4107C8.78648 32.4876 9.28628 30.6314 9.22655 29.8224C9.11353 28.2924 8.4718 26.6114 7.54341 25.3723C5.80722 23.0552 3.55502 21.1258 1.80167 18.8195C-0.311442 16.0399 -0.429357 11.3307 1.43344 8.78969C3.30091 6.24205 6.02143 5.85043 8.96553 6.2763C12.1877 6.74239 13.8883 9.52939 14.1458 14.3313Z" fill="white"></path>
                        <path d="M75.9623 6.69409C80.3525 6.69409 84.5125 6.69409 88.9343 6.69409C88.9343 8.03817 88.9343 9.3003 88.9343 10.9071C86.3368 10.9071 83.7085 10.9071 80.8656 10.9071C80.8656 13.742 80.8656 16.2389 80.8656 19.0538C83.0139 19.0538 84.9999 19.0538 87.1732 19.0538C87.1732 20.56 87.1732 21.7549 87.1732 23.0236H86.7326H81.0418C81.0418 24.4958 81.0418 28.5186 81.0418 32.0688C83.5819 32.0688 86.199 32.0688 89 32.0688C89 33.6284 89 34.8064 89 36.1713C84.6778 36.1713 80.4361 36.1713 75.962 36.1713C75.9623 26.4543 75.9623 16.729 75.9623 6.69409Z" fill="white"></path>
                        <path d="M54.9887 36.1713C53.2968 36.1713 51.9569 36.1713 50.4537 36.1713C50.4537 26.3101 50.4537 16.5916 50.4537 6.69409C51.9748 6.69409 53.394 6.69409 54.9887 6.69409C54.9887 16.516 54.9887 26.2395 54.9887 36.1713Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36348" x="-4" y="4" width="97" height="44.4067" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36348"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36348" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36348">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-TB" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36322)">
                    <g filter="url(#filter0_d_1701_36322)">
                        <path d="M24.9179 11.3333H2.95889L0.778564 14.6507C10.9015 14.6507 29.9015 14.4999 29.9015 14.4999C29.9015 14.4999 29.4343 13.5114 28.3441 12.6904C27.254 11.8694 26.1638 11.3333 24.9179 11.3333Z" fill="white"></path>
                        <path d="M11.213 14.3491H15.2622V43L11.213 41.7937V14.3491Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.5081 15.8571H20.5573V20.0793H24.4507C25.2294 20.0793 26.631 19.446 26.631 18.119V15.8571H30.5245V18.119C30.5245 20.5317 28.9671 21.4365 28.9671 21.4365C28.9671 21.4365 31.6146 22.492 31.6146 25.3571V28.0714C31.3032 30.0317 28.9819 33.1984 25.6966 33.1984H16.5081V15.8571ZM20.5573 23.6984H25.2294C26.1638 23.6984 27.7212 24.7841 27.7212 26.1111V27.3174C27.7212 28.0714 26.9425 29.7301 25.0737 29.7301H20.5573V23.6984Z" fill="white"></path>
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M19 41.7938C29.4934 41.7938 38 33.5572 38 23.3969C38 13.2366 29.4934 5 19 5C13.7322 5 8.96517 7.0757 5.52363 10.4285H11.0486C13.3761 9.08768 16.0952 8.31761 19.0001 8.31761C27.6012 8.31761 34.5739 15.0689 34.5739 23.397C34.5739 31.7252 27.6012 38.4764 19.0001 38.4764C18.043 38.4764 17.1061 38.3929 16.1967 38.2328V41.5949C17.1115 41.7259 18.0476 41.7938 19 41.7938ZM10.5902 39.8981V36.091C6.2809 33.4089 3.42632 28.7257 3.42632 23.397C3.42632 20.6503 4.18477 18.0751 5.50996 15.8571H1.66388C0.594811 18.158 0 20.7102 0 23.3969C0 30.6335 4.31528 36.8942 10.5902 39.8981Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36322" x="-4" y="3" width="46" height="46" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36322"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36322" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36322">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-V8CARD" viewBox="0 0 100 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_1701_36339)">
                    <g filter="url(#filter0_d_1701_36339)">
                        <path d="M22.2572 5.25285L22.5 6L26.5557 19.3042H26.7223L30.9999 6L31.2026 5.25285H35.4999L34.9999 6L29.0891 23.7471H24.1976L18.2504 6L17.4999 5.25285H22.2572Z" fill="white"></path>
                        <path d="M44.031 24C42.681 24 41.48 23.7742 40.4281 23.3227C39.382 22.8652 38.5609 22.2421 37.9648 21.4534C37.3687 20.6648 37.0707 19.7708 37.0707 18.7714C37.0707 18.0008 37.2402 17.2934 37.5791 16.6492C37.9239 15.999 38.3914 15.4602 38.9817 15.0328C39.5719 14.5993 40.2323 14.3224 40.9628 14.202V14.0756C40.0044 13.8769 39.2271 13.3983 38.631 12.6397C38.035 11.8752 37.7369 10.9872 37.7369 9.97576C37.7369 9.01854 38.0087 8.16667 38.5522 7.42015C39.0956 6.66762 39.8408 6.07763 40.7875 5.65019C41.7401 5.21673 42.8212 5 44.031 5C45.2407 5 46.3189 5.21673 47.2657 5.65019C48.2182 6.08365 48.9663 6.67665 49.5098 7.42918C50.0533 8.1757 50.328 9.02456 50.3338 9.97576C50.328 10.9932 50.0241 11.8812 49.4221 12.6397C48.8202 13.3983 48.0488 13.8769 47.1079 14.0756V14.202C47.8267 14.3224 48.4783 14.5993 49.0627 15.0328C49.653 15.4602 50.1205 15.999 50.4653 16.6492C50.8159 17.2934 50.9942 18.0008 51 18.7714C50.9942 19.7708 50.6932 20.6648 50.0971 21.4534C49.501 22.2421 48.677 22.8652 47.6251 23.3227C46.579 23.7742 45.3809 24 44.031 24ZM44.031 21.1283C44.6329 21.1283 45.1589 21.017 45.6089 20.7942C46.0589 20.5654 46.4095 20.2524 46.6608 19.855C46.9179 19.4517 47.0465 18.9881 47.0465 18.4644C47.0465 17.9285 46.915 17.456 46.652 17.0466C46.389 16.6312 46.0326 16.3061 45.5826 16.0713C45.1326 15.8305 44.6154 15.7101 44.031 15.7101C43.4524 15.7101 42.9352 15.8305 42.4794 16.0713C42.0235 16.3061 41.6641 16.6312 41.4011 17.0466C41.144 17.456 41.0154 17.9285 41.0154 18.4644C41.0154 18.9881 41.1411 19.4517 41.3924 19.855C41.6437 20.2524 41.9972 20.5654 42.4531 20.7942C42.9089 21.017 43.4349 21.1283 44.031 21.1283ZM44.031 12.8655C44.5336 12.8655 44.9806 12.7601 45.3722 12.5494C45.7637 12.3387 46.0705 12.0467 46.2926 11.6735C46.5147 11.3002 46.6257 10.8698 46.6257 10.3821C46.6257 9.90051 46.5147 9.47909 46.2926 9.11787C46.0705 8.75063 45.7667 8.46467 45.3809 8.25998C44.9952 8.04927 44.5452 7.94392 44.031 7.94392C43.5225 7.94392 43.0725 8.04927 42.681 8.25998C42.2894 8.46467 41.9826 8.75063 41.7605 9.11787C41.5443 9.47909 41.4362 9.90051 41.4362 10.3821C41.4362 10.8698 41.5472 11.3002 41.7693 11.6735C41.9914 12.0467 42.2982 12.3387 42.6898 12.5494C43.0813 12.7601 43.5284 12.8655 44.031 12.8655Z" fill="white"></path>
                        <path d="M0 41.7871V26.2129H5.99049C7.14213 26.2129 8.12324 26.4385 8.93384 26.8897C9.74443 27.3359 10.3623 27.9569 10.7873 28.7529C11.2173 29.5437 11.4323 30.4563 11.4323 31.4905C11.4323 32.5247 11.2149 33.4373 10.7799 34.2281C10.345 35.019 9.71478 35.635 8.88935 36.076C8.06887 36.5171 7.0754 36.7376 5.90894 36.7376H2.09074V34.0989H5.38996C6.00779 34.0989 6.51688 33.9899 6.91724 33.7719C7.32253 33.5488 7.62404 33.2421 7.82174 32.8517C8.02439 32.4563 8.12572 32.0025 8.12572 31.4905C8.12572 30.9734 8.02439 30.5222 7.82174 30.1369C7.62404 29.7465 7.32253 29.4449 6.91724 29.2319C6.51194 29.0139 5.9979 28.9049 5.37513 28.9049H3.21025V41.7871H0Z" fill="white"></path>
                        <path d="M27.4706 34C27.4706 35.6984 27.1568 37.1432 26.5291 38.3346C25.9063 39.526 25.0561 40.436 23.9786 41.0646C22.9061 41.6882 21.7001 42 20.3606 42C19.0113 42 17.8003 41.6857 16.7278 41.057C15.6552 40.4284 14.8076 39.5184 14.1848 38.327C13.562 37.1356 13.2506 35.6933 13.2506 34C13.2506 32.3016 13.562 30.8568 14.1848 29.6654C14.8076 28.474 15.6552 27.5665 16.7278 26.943C17.8003 26.3143 19.0113 26 20.3606 26C21.7001 26 22.9061 26.3143 23.9786 26.943C25.0561 27.5665 25.9063 28.474 26.5291 29.6654C27.1568 30.8568 27.4706 32.3016 27.4706 34ZM24.2159 34C24.2159 32.8999 24.0553 31.9721 23.734 31.2167C23.4177 30.4613 22.9703 29.8885 22.3921 29.4981C21.8138 29.1077 21.1366 28.9125 20.3606 28.9125C19.5846 28.9125 18.9075 29.1077 18.3292 29.4981C17.7509 29.8885 17.3011 30.4613 16.9799 31.2167C16.6635 31.9721 16.5054 32.8999 16.5054 34C16.5054 35.1001 16.6635 36.0279 16.9799 36.7833C17.3011 37.5387 17.7509 38.1115 18.3292 38.5019C18.9075 38.8923 19.5846 39.0875 20.3606 39.0875C21.1366 39.0875 21.8138 38.8923 22.3921 38.5019C22.9703 38.1115 23.4177 37.5387 23.734 36.7833C24.0553 36.0279 24.2159 35.1001 24.2159 34Z" fill="white"></path>
                        <path d="M29.8487 41.7871V26.2129H33.0589V33.0798H33.2591L38.7232 26.2129H42.571L36.9364 33.1863L42.6378 41.7871H38.7973L34.6381 35.384L33.0589 37.3612V41.7871H29.8487Z" fill="white"></path>
                        <path d="M44.2429 41.7871V26.2129H54.4742V28.9278H47.4531V32.6388H53.9478V35.3536H47.4531V39.0722H54.5038V41.7871H44.2429Z" fill="white"></path>
                        <path d="M57.0264 41.7871V26.2129H63.0169C64.1636 26.2129 65.1423 26.4233 65.9529 26.8441C66.7684 27.2598 67.3887 27.8504 67.8138 28.616C68.2438 29.3764 68.4588 30.2712 68.4588 31.3004C68.4588 32.3346 68.2413 33.2243 67.8064 33.9696C67.3714 34.7098 66.7412 35.2776 65.9158 35.673C65.0953 36.0684 64.1018 36.2662 62.9354 36.2662H58.9244V33.6198H62.4164C63.0293 33.6198 63.5384 33.5336 63.9437 33.3612C64.349 33.1888 64.6505 32.9303 64.8482 32.5856C65.0508 32.2408 65.1522 31.8124 65.1522 31.3004C65.1522 30.7833 65.0508 30.3473 64.8482 29.9924C64.6505 29.6375 64.3465 29.3688 63.9363 29.1863C63.531 28.9987 63.0194 28.9049 62.4016 28.9049H60.2367V41.7871H57.0264ZM65.2263 34.6996L69 41.7871H65.4561L61.764 34.6996H65.2263Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1701_36339" x="-4" y="3" width="77" height="45" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1701_36339"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1701_36339" result="shape"></feblend>
                    </filter>
                    <clippath id="clip0_1701_36339">
                        <rect width="100" height="48" fill="white"></rect>
                    </clippath>
                </defs>
            </symbol>
            <symbol id="icon-WICKETS9" viewBox="0 0 50 47" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_1851_26894)">
                    <path d="M5.39548 17.4099C5.55758 16.7315 5.75551 15.8605 6.03674 15.1285C7.30808 11.8227 9.25429 8.94232 11.9113 6.56318C13.7459 4.92055 15.8281 3.62745 18.12 2.68695C19.6216 2.07077 21.1788 1.65248 22.7881 1.3717C23.6859 1.21508 24.5891 1.14517 25.4857 1.05807C26.5222 0.957219 27.5837 0.962951 28.6855 1.27734C28.1667 1.55659 27.6305 1.68915 27.1337 1.8706C23.8331 3.07507 20.8048 4.77118 17.9767 6.80652C16.4172 7.92885 14.9117 9.12722 13.4952 10.4379C12.4127 11.4395 11.2785 12.3938 10.2873 13.4802C8.76136 15.153 7.12497 16.7341 5.80535 18.5846C5.66318 18.784 5.354 19.234 5.17128 19.4055C5.1712 18.6415 5.23337 18.0883 5.39548 17.4099Z" fill="white"></path>
                    <path d="M4.97813 26.4726C4.80236 25.8702 4.65899 25.5658 4.59178 24.9446C4.48168 23.9319 4.87194 22.938 5.49815 22.2091C6.60646 20.9194 7.62631 19.5599 8.72187 18.2576C10.5522 16.0813 12.5432 14.0609 14.6316 12.1424C15.9937 10.891 17.4578 9.73999 18.9756 8.65127C20.5444 7.52587 22.1576 6.48604 23.8566 5.58603C25.8673 4.52061 27.9576 3.61068 30.1812 3.04798C31.2033 2.78936 32.2363 2.57124 33.2608 2.32179C33.6116 2.23622 33.8774 2.46466 34.1667 2.55405C34.6376 2.69921 35.0618 2.98991 35.513 3.20307C35.62 3.25349 35.7421 3.26878 35.7204 3.41509C35.7 3.55414 35.5763 3.57171 35.4601 3.58699C32.5512 3.97205 29.854 5.04206 27.248 6.27709C24.7679 7.45215 22.4365 8.90339 20.2261 10.5533C17.4736 12.6077 14.8904 14.8394 12.5278 17.3095C11.0849 18.818 9.70772 20.3884 8.41051 22.0318C7.24618 23.5068 6.11121 24.9985 5.01488 26.5212C4.99363 26.5502 4.97813 26.4726 4.97813 26.4726Z" fill="white"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0.0273047 40.9076C-0.00641378 41.0364 -0.043868 41.3709 0.148606 41.3786C0.452849 40.9221 0.823347 40.4911 1.19107 40.0633C1.29108 39.9469 1.39088 39.8308 1.48909 39.7145C2.83382 38.1227 4.32264 36.6676 5.92813 35.3318C6.01862 35.2565 6.10243 35.1878 6.1826 35.122C6.42999 34.9191 6.64268 34.7446 6.90964 34.4948C7.14375 34.2759 7.49035 34.0899 7.74879 34.5158C8.43719 35.6496 9.18315 36.7609 10.0778 37.7441C11.3503 39.1423 12.7507 40.422 14.3658 41.4359C16.2444 42.6147 18.1964 43.6423 20.376 44.2142C20.6209 44.2785 20.887 44.3676 21.1472 44.4547C21.4959 44.5714 21.834 44.6846 22.0966 44.7299C22.555 44.809 23.9071 45 24.6797 45H27.7702C27.9227 45 28.2131 44.974 28.573 44.9418C28.8392 44.918 29.1433 44.8908 29.458 44.8682C30.0034 44.829 30.5205 44.6823 31.0391 44.5353C31.2244 44.4827 31.41 44.4301 31.597 44.3823C33.658 43.8563 35.5818 43.0323 37.3766 41.9248C38.4995 41.2319 39.5352 40.4213 40.5655 39.5873C41.8484 38.5494 42.8521 37.293 43.8213 36.0312C44.6692 34.9276 45.3638 33.6769 45.9274 32.3746C46.4582 31.148 46.9353 29.916 47.2556 28.6149C47.4571 27.7971 47.5827 26.9731 47.7086 26.1472C47.7466 25.8984 47.7845 25.6494 47.8246 25.4003C48.1317 23.4926 47.988 21.6051 47.7562 19.7359C47.536 17.96 47.0029 16.2272 46.2998 14.5609C45.4372 12.5171 44.2625 10.6579 42.8988 8.90756C42.1951 8.00395 41.3485 7.24222 40.5019 6.48049C40.2072 6.21526 39.9124 5.95004 39.6237 5.67883C39.3019 5.37628 38.8951 5.07373 38.5042 5.13906C38.1268 5.2021 37.7479 5.25315 37.3687 5.30422C36.8104 5.37942 36.2516 5.45469 35.6965 5.56843C33.3794 6.04327 31.1705 6.80155 29.018 7.74816C24.5844 9.69793 20.7098 12.4858 17.153 15.6851C14.7166 17.8771 12.4443 20.2455 10.4027 22.8245C8.77405 24.882 7.18633 26.9639 5.72918 29.1406C3.86873 31.9197 2.55452 34.9681 1.25151 38.0245C1.13263 38.3032 0.982446 38.6522 0.828321 39.0104C0.451544 39.8859 0.0512344 40.8161 0.0273047 40.9076ZM27.7245 29.5618C28.5942 29.5285 29.1205 29.5084 30.088 28.7647C30.2615 28.6314 30.481 28.4538 30.6429 28.2945C30.6609 28.2696 30.6738 28.2524 30.6738 28.2524C30.6738 28.2524 30.6921 28.2618 30.6675 28.3827C30.6429 28.5035 30.4743 28.9557 30.4393 28.9909C30.4267 29.0035 30.3784 29.1255 30.3106 29.2966C30.1896 29.6019 30.0069 30.0633 29.8556 30.3363C29.2985 31.3414 28.5101 32.1802 27.6227 32.89C26.2131 34.0177 24.6833 34.9353 22.7522 34.8657C22.3352 34.8508 21.9175 34.8545 21.4998 34.8582C21.13 34.8615 20.7602 34.8647 20.3907 34.8551C19.9723 34.844 19.7228 34.9372 19.5791 35.388C19.4205 35.8848 19.1974 36.3622 18.9746 36.8388C18.9119 36.9731 18.8491 37.1073 18.7879 37.2418C18.6175 37.6158 18.6836 37.7927 19.1367 37.785C19.9932 37.7705 20.8504 37.7705 21.7068 37.7831C23.4232 37.8084 25.1067 37.5983 26.7319 37.0497C28.3107 36.5168 29.7181 35.6817 31.0697 34.7087C32.9889 33.3274 34.5739 31.6714 35.7097 29.6257C36.2456 28.6601 36.6462 27.6285 37.0467 26.5972C37.1315 26.3789 37.2163 26.1605 37.3024 25.9428C37.7786 24.7384 38.2761 23.5421 38.7736 22.3459C39.3463 20.9686 39.9191 19.5914 40.4593 18.2018C40.8174 17.2812 40.8197 16.2979 40.4075 15.3345C39.718 13.7236 38.2801 13.3805 36.7743 13.298C35.2035 13.2121 33.6248 13.2453 32.0491 13.2784C31.8879 13.2818 31.7268 13.2852 31.5658 13.2884C31.0237 13.2994 30.4793 13.415 29.9434 13.5288C29.9026 13.5375 29.8619 13.5461 29.8212 13.5547C28.6209 13.8084 27.5563 14.3837 26.621 15.1305C25.4718 16.0485 24.3812 17.0501 23.7987 18.4505C23.6244 18.8695 23.447 19.2873 23.2697 19.7052C22.802 20.8072 22.3342 21.9091 21.9228 23.0312C21.8356 23.269 21.7388 23.5044 21.642 23.7399C21.2483 24.6979 20.8538 25.6578 21.0972 26.7905C21.4754 28.5508 22.4385 29.4344 24.1896 29.5394C24.7605 29.5736 25.3341 29.5639 25.9078 29.5542C26.47 29.5447 27.0322 29.5352 27.5918 29.5669C27.637 29.5651 27.6812 29.5634 27.7245 29.5618Z" fill="white"></path>
                    <path d="M33.0025 16.3861C34.3689 16.2047 35.3648 17.3221 34.6084 18.8837C33.8289 20.4927 33.2073 22.18 32.3682 23.7562C31.5496 25.2942 30.1589 26.1071 28.3588 26.0757C27.2172 26.0559 26.472 24.9397 26.9167 23.9323C27.702 22.1533 28.5361 20.3949 29.3253 18.6178C29.7471 17.6685 31.8606 16.1527 33.0025 16.3861Z" fill="white"></path>
                </g>
                <defs>
                    <filter id="filter0_d_1851_26894" x="-2" y="0" width="52" height="48" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="1"></feoffset>
                        <fegaussianblur stdDeviation="1"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1851_26894"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1851_26894" result="shape"></feblend>
                    </filter>
                </defs>
            </symbol>
            <symbol id="icon-WM" viewBox="0 0 42 45" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_1802_27510)">
                    <path d="M30.513 32.596C27.556 35.532 23.4864 37.3489 19 37.3489C9.98524 37.3489 2.65114 30.0148 2.65114 21C2.65114 18.7934 3.09155 16.688 3.88745 14.7657L8.36722 26.2611L8.37112 26.2712L8.3835 26.2669L8.39587 26.2712L8.39978 26.2611L11.8868 17.3131L15.3738 26.2611L15.3777 26.2712L15.3901 26.2669L15.4025 26.2712L15.4064 26.2611L21.5033 10.6162H18.631L15.3901 18.9326L11.9155 10.0165H11.8581L8.3835 18.9326L4.43849 8.8096C3.75143 9.62895 3.13217 10.5067 2.58928 11.4346C0.944389 14.2458 0 17.5145 0 21C0 31.4767 8.52344 40.0001 19 40.0001C23.8057 40.0001 28.1995 38.2058 31.5491 35.2531L30.513 32.596Z" fill="white"></path>
                    <path d="M19.0001 2C14.0887 2 9.60649 3.87323 6.23059 6.94264L7.27438 9.61983C10.2474 6.55742 14.405 4.65114 19.0001 4.65114C28.0149 4.65114 35.349 11.9852 35.349 21C35.349 23.296 34.8723 25.4825 34.0142 27.467L29.5461 16.0445L29.5422 16.0344L29.5298 16.0387L29.5175 16.0344L29.5136 16.0445L26.0265 24.9924L22.5395 16.0444L22.5354 16.0343L22.5231 16.0385L22.5107 16.034L22.5068 16.0436L16.4336 31.6258H19.3034L22.5231 23.3598L25.9976 32.236H26.0551L29.5297 23.3465L33.4013 33.38C34.1049 32.5625 34.7401 31.6846 35.2983 30.7555C37.0127 27.902 38 24.5644 38 21.0001C38.0001 10.5234 29.4767 2 19.0001 2Z" fill="white"></path>
                </g>
                <defs>
                    <filter id="filter0_d_1802_27510" x="-4" y="0" width="46" height="46.0001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.552941 0 0 0 0 0.172549 0 0 0 0 0.172549 0 0 0 0.25 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1802_27510"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1802_27510" result="shape"></feblend>
                    </filter>
                </defs>
            </symbol>
            <!--?xml version="1.0" standalone="no"?-->
            <symbol id="icon-home" class="icon" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="27" cy="28" r="18" fill="#FFF4F4"></circle>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M23.6595 5.2783L5.75999 17.3577V42.8647H15.84V32.4247C15.84 29.6412 18.0965 27.3847 20.88 27.3847H27.24C30.0235 27.3847 32.28 29.6412 32.28 32.4247V36.8047H28.92V32.4247C28.92 31.4968 28.1678 30.7447 27.24 30.7447H20.88C19.9522 30.7447 19.2 31.4968 19.2 32.4247V43.5847C19.2 45.0427 18.018 46.2247 16.56 46.2247H5.03999C3.58196 46.2247 2.39999 45.0427 2.39999 43.5847V16.975C2.39999 16.0976 2.83592 15.2775 3.56322 14.7867L22.4373 2.04955C23.1548 1.56531 24.0922 1.55624 24.819 2.02652L44.5142 14.7705C45.266 15.257 45.72 16.0914 45.72 16.9869V43.5847C45.72 45.0427 44.538 46.2247 43.08 46.2247H30.6V42.8647H42.36V17.3786L23.6595 5.2783Z" fill="#FFCDCB"></path>
                <path d="M32.4 44.5447C32.4 45.4726 31.6478 46.2247 30.72 46.2247C29.7922 46.2247 29.04 45.4726 29.04 44.5447C29.04 43.6169 29.7922 42.8647 30.72 42.8647C31.6478 42.8647 32.4 43.6169 32.4 44.5447Z" fill="#FFCDCB"></path>
                <path d="M32.28 36.7447C32.28 37.6726 31.5278 38.4247 30.6 38.4247C29.6722 38.4247 28.92 37.6726 28.92 36.7447C28.92 35.8169 29.6722 35.0647 30.6 35.0647C31.5278 35.0647 32.28 35.8169 32.28 36.7447Z" fill="#FFCDCB"></path>
            </symbol>
            <symbol id="icon-main" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="28" cy="24" r="18" fill="#FFF4F4"></circle>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M24.0801 5.27992C13.7413 5.27992 5.36006 13.6612 5.36006 23.9999C5.36006 34.3387 13.7413 42.7199 24.0801 42.7199C34.4188 42.7199 42.8001 34.3387 42.8001 23.9999V15.2399H46.1601V23.9999C46.1601 36.1944 36.2745 46.0799 24.0801 46.0799C11.8856 46.0799 2.00006 36.1944 2.00006 23.9999C2.00006 11.8055 11.8856 1.91992 24.0801 1.91992H44.3601V5.27992H24.0801Z" fill="#FFCDCB"></path>
                <path d="M46.1601 3.59992C46.1601 4.52776 45.4079 5.27992 44.4801 5.27992C43.5522 5.27992 42.8001 4.52776 42.8001 3.59992C42.8001 2.67208 43.5522 1.91992 44.4801 1.91992C45.4079 1.91992 46.1601 2.67208 46.1601 3.59992Z" fill="#FFCDCB"></path>
                <path d="M46.1601 15.1199C46.1601 16.0478 45.4079 16.7999 44.4801 16.7999C43.5522 16.7999 42.8001 16.0478 42.8001 15.1199C42.8001 14.1921 43.5522 13.4399 44.4801 13.4399C45.4079 13.4399 46.1601 14.1921 46.1601 15.1199Z" fill="#FFCDCB"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.8061 29.5823C16.5007 28.9671 17.5624 29.0315 18.1776 29.726C19.3272 31.0239 20.9262 32.5199 23.64 32.5199C26.5114 32.5199 28.4968 31.0922 29.4445 29.9866C30.0483 29.2821 31.1089 29.2006 31.8133 29.8044C32.5178 30.4082 32.5994 31.4688 31.9956 32.1733C30.5432 33.8676 27.6806 35.8799 23.64 35.8799C19.4418 35.8799 16.9928 33.4559 15.6624 31.9538C15.0472 31.2593 15.1115 30.1975 15.8061 29.5823Z" fill="#FFCDCB"></path>
            </symbol>
            <symbol id="icon-nbg" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_186_36273" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="68" height="68">
                    <path d="M33.5569 68.5569C52.0898 69.3539 67.7599 54.9761 68.5569 36.4431C69.3539 17.9102 54.9761 2.24013 36.4431 1.44312C17.9102 0.646107 2.24012 15.0239 1.44311 33.5569C0.646096 52.0898 15.0239 67.7599 33.5569 68.5569Z" fill="url(#paint0_linear_186_36273)"></path>
                </mask>
                <g mask="url(#mask0_186_36273)">
                    <path d="M35 68.5879C53.5501 68.5879 68.5879 53.5501 68.5879 35C68.5879 16.4499 53.5501 1.41211 35 1.41211C16.4499 1.41211 1.41211 16.4499 1.41211 35C1.41211 53.5501 16.4499 68.5879 35 68.5879Z" fill="url(#paint1_linear_186_36273)"></path>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M20.8571 54.4386C14.5344 49.7312 10.5774 42.0774 10.9416 33.6084C11.5261 20.0175 23.0175 9.47379 36.6083 10.0583C50.1991 10.6427 60.7429 22.1341 60.1584 35.7249C59.8057 43.9262 55.4813 51.0179 49.1206 55.2243C57.638 50.6342 63.5916 41.8044 64.0375 31.4358C64.7112 15.771 52.5585 2.52612 36.8937 1.85245C21.229 1.17879 7.98407 13.3315 7.3104 28.9962C6.85128 39.6721 12.3497 49.2241 20.8571 54.4386Z" fill="url(#paint2_linear_186_36273)"></path>
                    <path d="M34.4918 59.275C48.0826 59.8595 59.574 49.3158 60.1585 35.725C60.7429 22.1341 50.1992 10.6428 36.6084 10.0583C23.0175 9.47381 11.5262 20.0175 10.9417 33.6084C10.3572 47.1992 20.901 58.6906 34.4918 59.275Z" fill="url(#paint3_linear_186_36273)"></path>
                    <path d="M-27.8454 35.321C-32.8649 44.6572 -29.3655 56.2949 -20.0292 61.3144C-10.693 66.3338 0.944652 62.8344 5.96414 53.4982C10.9836 44.1619 7.4842 32.5243 -1.85205 27.5048C-11.1883 22.4853 -22.8259 25.9847 -27.8454 35.321Z" fill="url(#paint4_linear_186_36273)"></path>
                    <path d="M94.1708 28.2582C100.556 35.7018 99.6987 46.9127 92.2551 53.2983C84.8115 59.6839 73.6007 58.8262 67.215 51.3826C60.8294 43.939 61.6871 32.7282 69.1307 26.3425C76.5743 19.9569 87.7852 20.8146 94.1708 28.2582Z" fill="url(#paint5_linear_186_36273)"></path>
                    <path d="M24.5591 -25.4224C32.3161 -31.4235 43.4692 -30 49.4703 -22.243C55.4713 -14.486 54.0479 -3.33291 46.2909 2.66816C38.5339 8.66923 27.3808 7.24577 21.3797 -0.511225C15.3786 -8.26822 16.8021 -19.4213 24.5591 -25.4224Z" fill="url(#paint6_linear_186_36273)"></path>
                    <path d="M23.1952 79.4598C22.7739 89.2581 30.3753 97.5427 40.1736 97.9641C49.9719 98.3855 58.2566 90.784 58.6779 80.9857C59.0993 71.1875 51.4978 62.9028 41.6996 62.4814C31.9013 62.06 23.6166 69.6615 23.1952 79.4598Z" fill="url(#paint7_linear_186_36273)"></path>
                    <g filter="url(#filter0_f_186_36273)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M18.9941 54.2005C33.1553 63.2088 51.9376 59.0322 60.9459 44.8715C62.7462 42.0417 64.0197 39.0272 64.7923 35.9438C64.3629 40.3674 62.913 44.7596 60.3637 48.766C51.7109 62.368 33.6703 66.3801 20.0681 57.7273C13.8697 53.7841 9.66276 47.8911 7.73828 41.3577C10.0995 46.4563 13.9011 50.9604 18.9941 54.2005Z" fill="#B6B6B6"></path>
                    </g>
                    <g filter="url(#filter1_f_186_36273)">
                        <path d="M31.4826 61.6469C31.5328 62.5288 33.3608 63.142 35.5656 63.0166C37.7704 62.8911 39.517 62.0745 39.4668 61.1925C39.4167 60.3106 37.5886 59.6974 35.3839 59.8229C33.1791 59.9483 31.4325 60.765 31.4826 61.6469Z" fill="white"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_f_186_36273" x="4.73828" y="32.9438" width="63.0542" height="32.3481" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="1.5" result="effect1_foregroundBlur_186_36273"></fegaussianblur>
                    </filter>
                    <filter id="filter1_f_186_36273" x="25.4814" y="53.8066" width="19.9863" height="15.2263" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="3" result="effect1_foregroundBlur_186_36273"></fegaussianblur>
                    </filter>
                    <lineargradient id="paint0_linear_186_36273" x1="20.1375" y1="8.34639" x2="44.0641" y2="66.2072" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F22427"></stop>
                        <stop offset="1" stop-color="#960204"></stop>
                    </lineargradient>
                    <lineargradient id="paint1_linear_186_36273" x1="28.7079" y1="6.55778" x2="45.3966" y2="65.7889" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#BABABA"></stop>
                        <stop offset="0.538037" stop-color="#AAAAAA"></stop>
                        <stop offset="1" stop-color="#9D9D9D"></stop>
                    </lineargradient>
                    <lineargradient id="paint2_linear_186_36273" x1="28.9373" y1="3.46916" x2="38.5326" y2="60.4738" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#DCDCDC"></stop>
                        <stop offset="0.743552" stop-color="#C2C2C2"></stop>
                        <stop offset="1" stop-color="#CDCDCD" stop-opacity="0"></stop>
                    </lineargradient>
                    <lineargradient id="paint3_linear_186_36273" x1="30.1694" y1="11.6563" x2="35.5173" y2="58.6479" gradientUnits="userSpaceOnUse">
                        <stop offset="0.349342" stop-color="#FDFDFD"></stop>
                        <stop offset="1" stop-color="#E1E1E1"></stop>
                    </lineargradient>
                    <lineargradient id="paint4_linear_186_36273" x1="9.74742" y1="40.5508" x2="-28.0715" y2="49.2971" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.373872" stop-color="#EAEAEA"></stop>
                    </lineargradient>
                    <lineargradient id="paint5_linear_186_36273" x1="62.1375" y1="40.3055" x2="79" y2="41.9999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.744724" stop-color="#CDCDCD"></stop>
                    </lineargradient>
                    <lineargradient id="paint6_linear_186_36273" x1="36.9468" y1="5.59797" x2="36.9468" y2="-11.196" gradientUnits="userSpaceOnUse">
                        <stop offset="0.0664686" stop-color="#FBF9FD"></stop>
                        <stop offset="1" stop-color="#BFBFBF"></stop>
                    </lineargradient>
                    <lineargradient id="paint7_linear_186_36273" x1="41" y1="63.5" x2="39" y2="81.5" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#DBDBDB"></stop>
                        <stop offset="0.373872" stop-color="#D6D6D6"></stop>
                    </lineargradient>
                </defs>
            </symbol>
            <symbol id="icon-play" viewBox="0 0 272 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_1_113)">
                    <path d="M53.6145 9.98864C55.1132 4.28373 60.1749 0 66.0734 0H205.084C211.342 0 216.611 4.79619 217.905 10.9189C221.257 26.7904 230.682 55.876 256 70H16C38.9518 55.5953 49.5057 25.6286 53.6145 9.98864Z" fill="white"></path>
                </g>
                <defs>
                    <filter id="filter0_d_1_113" x="0" y="-8" width="272" height="102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="8"></feoffset>
                        <fegaussianblur stdDeviation="8"></fegaussianblur>
                        <fecomposite in2="hardAlpha" operator="out"></fecomposite>
                        <fecolormatrix type="matrix" values="0 0 0 0 0.815686 0 0 0 0 0.815686 0 0 0 0 0.929412 0 0 0 0.36 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_113"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_113" result="shape"></feblend>
                    </filter>
                </defs>
            </symbol>
            <symbol id="icon-playactive" viewBox="0 0 240 70" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M37.6145 9.98864C39.1132 4.28373 44.1749 0 50.0734 0H189.084C195.342 0 200.611 4.79619 201.905 10.9189C205.257 26.7904 214.682 55.876 240 70H0C22.9518 55.5953 33.5057 25.6286 37.6145 9.98864Z" fill="url(#paint0_linear_1_117)"></path>
                <defs>
                    <lineargradient id="paint0_linear_1_117" x1="127.5" y1="2.98875e-06" x2="161" y2="70" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF8E8A"></stop>
                        <stop offset="1" stop-color="#FFC3A3"></stop>
                    </lineargradient>
                </defs>
            </symbol>
            <symbol id="icon-promotion" viewBox="0 0 57 49" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M8.93876 1.50122C9.69785 0.55236 10.8471 0 12.0622 0H44.2172C45.4324 0 46.5816 0.552359 47.3407 1.50122L55.0792 11.1744C55.5056 11.7073 55.828 12.2943 56.0469 12.9092H0.232598C0.451468 12.2943 0.773925 11.7073 1.20023 11.1744L8.93876 1.50122ZM0 16.091H56.2795C56.0896 17.0496 55.664 17.9709 55.0034 18.7637L31.2126 47.3125C29.6134 49.2316 26.666 49.2316 25.0669 47.3125L1.27612 18.7637C0.615521 17.9709 0.189841 17.0496 0 16.091ZM20.5563 22.0266L27.7513 32.1286C27.9512 32.4093 28.3685 32.4083 28.5671 32.1267L35.6853 22.0338C36.1425 21.3856 36.8863 21 37.6795 21C39.0272 21 40.1198 22.0925 40.1198 23.4403V23.6393H39.8972C39.5712 23.6393 39.1148 23.8877 38.5931 24.5708C38.0874 25.2331 32.1271 33.2938 28.9417 37.6047C28.7578 37.8535 28.467 38 28.1577 38C27.8515 38 27.5632 37.8562 27.379 37.6117L17.3204 24.2603C17.3204 24.2603 16.9258 23.6393 16.2608 23.6393H16.1198V23.445C16.1198 22.0947 17.2144 21 18.5648 21C19.3556 21 20.0975 21.3825 20.5563 22.0266Z" fill="white"></path>
            </symbol>
            <symbol id="icon-resultanbg" viewBox="0 0 60 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_1148_26793" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="1" y="1" width="58" height="58">
                    <path d="M28.7628 58.763C44.6481 59.4462 58.0796 47.1223 58.7628 31.237C59.4459 15.3516 47.1221 1.92012 31.2367 1.23696C15.3513 0.553806 1.91983 12.8777 1.23667 28.763C0.553518 44.6484 12.8774 58.0799 28.7628 58.763Z" fill="url(#paint0_linear_1148_26793)"></path>
                </mask>
                <g mask="url(#mask0_1148_26793)">
                    <g filter="url(#filter0_d_1148_26793)">
                        <path d="M28.7628 58.763C44.6481 59.4462 58.0796 47.1223 58.7628 31.237C59.4459 15.3516 47.1221 1.92012 31.2367 1.23696C15.3513 0.553806 1.91983 12.8777 1.23667 28.763C0.553518 44.6484 12.8774 58.0799 28.7628 58.763Z" fill="url(#paint1_linear_1148_26793)"></path>
                    </g>
                    <path d="M29.5321 50.2111C42.959 50.7885 54.3118 40.3719 54.8892 26.945C55.4667 13.518 45.0501 2.16525 31.6232 1.58782C18.1962 1.0104 6.84343 11.427 6.266 24.8539C5.68857 38.2809 16.1052 49.6336 29.5321 50.2111Z" fill="url(#paint2_linear_1148_26793)"></path>
                    <g filter="url(#filter1_f_1148_26793)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M16.2817 46.4576C28.4198 54.179 44.5189 50.599 52.2403 38.4613C53.7834 36.0357 54.875 33.4519 55.5372 30.809C55.1692 34.6006 53.9264 38.3653 51.7413 41.7994C44.3246 53.4583 28.8612 56.8972 17.2022 49.4805C11.8893 46.1006 8.28334 41.0495 6.63379 35.4494C8.65765 39.8197 11.9162 43.6803 16.2817 46.4576Z" fill="#F26565"></path>
                    </g>
                    <path d="M29.5638 50.8072C41.2131 51.3082 51.0629 42.2707 51.5638 30.6214C52.0648 18.9721 43.0273 9.12238 31.3781 8.6214C19.7288 8.12042 9.87903 17.1579 9.37805 28.8072C8.87707 40.4565 17.9146 50.3062 29.5638 50.8072Z" fill="url(#paint3_linear_1148_26793)"></path>
                    <path d="M-23.8685 30.2751C-28.1709 38.2776 -25.1714 48.2527 -17.1689 52.5552C-9.16639 56.8576 0.808726 53.8581 5.11115 45.8556C9.41357 37.8531 6.41405 27.878 -1.58845 23.5755C-9.59095 19.2731 -19.5661 22.2726 -23.8685 30.2751Z" fill="url(#paint4_linear_1148_26793)"></path>
                    <path d="M80.7175 24.2214C86.1909 30.6016 85.4558 40.2109 79.0755 45.6843C72.6953 51.1577 63.086 50.4225 57.6126 44.0423C52.1392 37.662 52.8744 28.0527 59.2546 22.5794C65.6349 17.106 75.2442 17.8411 80.7175 24.2214Z" fill="url(#paint5_linear_1148_26793)"></path>
                    <path d="M21.0502 -21.7906C27.6991 -26.9344 37.2589 -25.7143 42.4027 -19.0654C47.5464 -12.4166 46.3263 -2.85677 39.6775 2.28701C33.0286 7.43078 23.4688 6.21067 18.325 -0.438181C13.1813 -7.08703 14.4014 -16.6468 21.0502 -21.7906Z" fill="url(#paint6_linear_1148_26793)"></path>
                    <path d="M19.8815 68.1084C19.5203 76.5069 26.0359 83.6081 34.4344 83.9692C42.8329 84.3304 49.9341 77.8149 50.2952 69.4163C50.6564 61.0178 44.1409 53.9167 35.7423 53.5555C27.3438 53.1943 20.2427 59.7099 19.8815 68.1084Z" fill="url(#paint7_linear_1148_26793)"></path>
                    <g filter="url(#filter2_f_1148_26793)">
                        <path d="M26.9853 52.8402C27.0283 53.5961 28.5951 54.1217 30.4849 54.0142C32.3748 53.9067 33.8719 53.2067 33.8289 52.4508C33.7858 51.6948 32.219 51.1692 30.3292 51.2768C28.4394 51.3843 26.9422 52.0843 26.9853 52.8402Z" fill="white"></path>
                    </g>
                    <g style="mix-blend-mode:screen" filter="url(#filter3_f_1148_26793)">
                        <path d="M29.3201 21.8121C34.9243 22.3645 39.7834 19.6054 40.1733 15.6495C40.5632 11.6937 36.3362 8.03906 30.7321 7.48669C25.128 6.93432 20.2689 9.69339 19.879 13.6492C19.4891 17.6051 23.716 21.2597 29.3201 21.8121Z" fill="#FFCFCE"></path>
                    </g>
                </g>
                <defs>
                    <filter id="filter0_d_1148_26793" x="-2.79004" y="-4.79013" width="65.5801" height="65.5803" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"></fecolormatrix>
                        <feoffset dy="-2"></feoffset>
                        <fegaussianblur stdDeviation="2"></fegaussianblur>
                        <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.630158 0 0 0 0 0.630158 0 0 0 1 0"></fecolormatrix>
                        <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1148_26793"></feblend>
                        <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1148_26793" result="shape"></feblend>
                    </filter>
                    <filter id="filter1_f_1148_26793" x="3.63379" y="27.809" width="54.9033" height="28.5841" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="1.5" result="effect1_foregroundBlur_1148_26793"></fegaussianblur>
                    </filter>
                    <filter id="filter2_f_1148_26793" x="20.9844" y="45.2628" width="18.8457" height="14.7654" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="3" result="effect1_foregroundBlur_1148_26793"></fegaussianblur>
                    </filter>
                    <filter id="filter3_f_1148_26793" x="14.8545" y="2.41631" width="30.3438" height="24.4662" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feflood flood-opacity="0" result="BackgroundImageFix"></feflood>
                        <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"></feblend>
                        <fegaussianblur stdDeviation="2.5" result="effect1_foregroundBlur_1148_26793"></fegaussianblur>
                    </filter>
                    <lineargradient id="paint0_linear_1148_26793" x1="17.2604" y1="7.15404" x2="37.769" y2="56.749" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#F22427"></stop>
                        <stop offset="1" stop-color="#960204"></stop>
                    </lineargradient>
                    <lineargradient id="paint1_linear_1148_26793" x1="25.6589" y1="5.41173" x2="37.769" y2="56.749" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FB444C"></stop>
                        <stop offset="1" stop-color="#DF242E"></stop>
                    </lineargradient>
                    <lineargradient id="paint2_linear_1148_26793" x1="24.8033" y1="2.97361" x2="33.0278" y2="51.8347" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FA999A"></stop>
                        <stop offset="0.743552" stop-color="#FE474D"></stop>
                        <stop offset="1" stop-color="#DD2223" stop-opacity="0"></stop>
                    </lineargradient>
                    <lineargradient id="paint3_linear_1148_26793" x1="25.859" y1="9.99113" x2="30.4428" y2="50.2696" gradientUnits="userSpaceOnUse">
                        <stop offset="0.349342" stop-color="#FBF9FD"></stop>
                        <stop offset="0.889385" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint4_linear_1148_26793" x1="8.35395" y1="34.7578" x2="-24.0623" y2="42.2546" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.373872" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint5_linear_1148_26793" x1="53.2605" y1="34.5476" x2="66.6956" y2="34.0678" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FBF9FD"></stop>
                        <stop offset="0.601592" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint6_linear_1148_26793" x1="31.6682" y1="4.79827" x2="31.6682" y2="-9.59654" gradientUnits="userSpaceOnUse">
                        <stop offset="0.0664686" stop-color="#FBF9FD"></stop>
                        <stop offset="1" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                    <lineargradient id="paint7_linear_1148_26793" x1="48.5274" y1="58.8661" x2="24.5445" y2="78.165" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#EBA3A6"></stop>
                        <stop offset="0.373872" stop-color="#EBA3A5"></stop>
                    </lineargradient>
                </defs>
            </symbol>
            <symbol id="icon-subtract" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M25.0566 25.0562C22.4829 27.6299 18.9273 29.2218 15 29.2218C11.0727 29.2218 7.51711 27.6299 4.94336 25.0562C2.36965 22.4824 0.777771 18.9269 0.777771 14.9996C0.777771 11.0722 2.36965 7.51668 4.94336 4.94293C7.51711 2.36922 11.0727 0.777344 15 0.777344C18.9273 0.777344 22.4829 2.36922 25.0566 4.94293C27.6303 7.51668 29.2222 11.0722 29.2222 14.9996C29.2222 18.9269 27.6303 22.4824 25.0566 25.0562ZM22.7492 11.1747C23.091 10.8222 23.0823 10.2595 22.7299 9.91775C22.3775 9.576 21.8147 9.58465 21.473 9.93709L12.6296 17.279L8.52702 14.826C8.18527 14.4735 7.62252 14.4649 7.27009 14.8066C6.91766 15.1484 6.909 15.7111 7.25076 16.0636L11.9915 20.9525C12.1589 21.1251 12.3891 21.2225 12.6296 21.2225C12.8701 21.2225 13.1003 21.1251 13.2678 20.9525L22.7492 11.1747Z" fill="white"></path>
            </symbol>
            <symbol id="icon-wallet" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="27" cy="24" r="18" fill="#FFF4F4"></circle>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M10.5124 5.2H37.4875C40.0605 5.2 42.1759 7.22874 42.2834 9.79945L42.7936 22H45.9964L45.4806 9.66575C45.3014 5.38123 41.7758 2 37.4875 2H10.5124C6.22418 2 2.6986 5.38123 2.51943 9.66575L1.34852 37.6657C1.15843 42.2112 4.79211 46 9.34153 46H38.6585C43.2079 46 46.8416 42.2112 46.6515 37.6657L46.3309 30H43.1281L43.4543 37.7994C43.5683 40.5267 41.3881 42.8 38.6585 42.8H9.34153C6.61188 42.8 4.43167 40.5267 4.54572 37.7994L5.71663 9.79945C5.82413 7.22874 7.93948 5.2 10.5124 5.2Z" fill="#FFCDCB"></path>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M13.9209 16.6399C14.3857 21.7979 18.7206 25.8399 23.9996 25.8399C29.2786 25.8399 33.6136 21.7979 34.0784 16.6399H30.5562C30.1084 19.8606 27.3436 22.3399 23.9996 22.3399C20.6557 22.3399 17.8908 19.8606 17.4431 16.6399H13.9209Z" fill="#FFCDCB"></path>
                <path d="M34.0918 16.6494C34.0918 17.6159 33.3083 18.3994 32.3418 18.3994C31.3753 18.3994 30.5469 17.6159 30.5469 16.6494C30.5469 15.6829 31.3753 14.8994 32.3418 14.8994C33.3083 14.8994 34.0918 15.6829 34.0918 16.6494Z" fill="#FFCDCB"></path>
                <path d="M17.4488 16.6478C17.4488 17.6143 16.6829 18.3978 15.7164 18.3978C14.7499 18.3978 13.9215 17.6143 13.9215 16.6478C13.9215 15.6813 14.7499 14.8978 15.7164 14.8978C16.6829 14.8978 17.4488 15.6813 17.4488 16.6478Z" fill="#FFCDCB"></path>
                <path d="M46 22C46 22.8837 45.2837 23.6 44.4 23.6C43.5163 23.6 42.8 22.8837 42.8 22C42.8 21.1163 43.5163 20.4 44.4 20.4C45.2837 20.4 46 21.1163 46 22Z" fill="#FFCDCB"></path>
                <path d="M46.3309 30.044C46.3309 30.9276 45.6146 31.644 44.7309 31.644C43.8472 31.644 43.1309 30.9276 43.1309 30.044C43.1309 29.1603 43.8472 28.444 44.7309 28.444C45.6146 28.444 46.3309 29.1603 46.3309 30.044Z" fill="#FFCDCB"></path>
            </symbol>
        </svg>
    <div id="app" data-v-app="">
        <div data-v-1690b988="" class="ar-loading-view" style="display: none; --7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
            <div data-v-1690b988="" class="loading-wrapper">
                <div data-v-1690b988="" class="loading-animat">
                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet" style="width: 100%; height: 100%; transform: translate3d(0px, 0px, 0px); content-visibility: visible;">
                            <defs>
                                <clippath id="__lottie_element_2">
                                    <rect width="200" height="200" x="0" y="0"></rect>
                                </clippath>
                                <image href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKgAAAAsBAMAAAADRO5JAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJFBMVEXzwwBHcEzzwgDzwwD0wwDzwwD0xADzwwDzwwDxwQDzwwD0wwDcY0swAAAADHRSTlP/AD+/In+jY9wQT49dhnwkAAADuUlEQVRIx+WXz08TQRTHh/4GLky7tAUutNaq4QKCJtxaKFh7ag2NopdWSJVwKYJoPLWxAY0XNiTG6EUuJN6oF/8934+Z2S22tCQ1MXEOzM7uzGfevPd9r4OQA7fT7KAzxaATZ0p3d/alPVTox0ih/TQvW8OEXmxUzsql2e3iwkDQG4t9W8kKHK/lWrcPlta/1lrRvvOXRF30bb7p3XZeri6/zJ4mnj3tv8AnVpJ9W0pOHb0Prm5vFUL3m7L/gsSAPn1gn21nUjs/CvZQJSW3X7Srw9apXN3Lnw0deo32r0Ffl8tljtvNcvkxd9Q2RbqG7QDqBer2wFkztXJ+5Gbkbn1QFeW8RN08SNI8haArGp0qLe/IDPXHGnkIA6+DnGvAWEEbHgNt4oMlekBDNkOFKhk0wYGu0jeGhkXAQAkf6wUVVQXllXnRAY25PsmIELaGjuNDtCfUo6E+dSA3tOiGZpQrEUr4k27Q0CLuqaEhWyr/Guik+xCyIsSogWI+1B1okHMf3gTxtAEABbZwXhtCgf2vcw2dxeG9VwzFUwQNdIF30VAf65SgYbAqg6/W6PzoQm9CWu8UFPfYlPKNrT3oNVC0WXSHTrGlPjqKX8bZXt0soWebGCY01C/ldA8omBZk6Ax+z/Ak6XLpnhnV9annldujLugY+pShOfjTZOgURrSuBKhapGOPhpYIQcHmCXEp+tJIKsFQXJO9EGJE7kPWsn1x1CNmcdVI1m+gbfm9J3RMKiiEMlFBaMWk9gRCMiopYQcv65OhozSxOzSgofMa+g12sjU0gNEKZFleniJtBzMb6KcG9t2P33Ydn8I4aRwbp3gITnlANU/IaHiCk3tBjqHin9DxJPzxMRRdxrpyoFE6LkNRXj/T9AmgczBah0gb6DjWUyUpAPgZmleeELYDjVE8GBoVJoMBmlLJXrxS/FYDYwYaEssOlBOTobpCgJSU97HCdYVanKaeQoVijDkSOnTEinF+ztCKhlYJyntku0I/s6XaBqUWA81ohdBJhJISQqPsii5Qb01wmhpdf+mExgw0Zi5MfoJS4Q3KzuiPGEm1NBQriTLI05HvCI071zB7XjkW/NULupdxDNW/SQY63VBQnP0omUzjAoLOUrHpAQ0qx3kTjHnbAZXrCqp+U7FuLRA0Qi+6Q4FF0E8JXUOfdEBlmqBhRlPvIWiYXlyG0nUzBUlu5XK5h66CB6/vuIYreE+16LYKBRL7w1oti/eUDbhe1HbxklHjVv0vLmhXQAf4R+K6zfdXoL8B/7cyv853sCEAAAAASUVORK5CYII="></image>
                                <image href="data:image/png;base64,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"></image>
                            </defs>
                            <g clip-path="url(#__lottie_element_2)">
                                <g class="ai" transform="matrix(0.5677862763404846,0.8231760263442993,-0.8231760263442993,0.5677862763404846,125.53897094726562,-39.096221923828125)" opacity="1" style="display: block;">
                                    <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice" href="data:image/png;base64,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"></image>
                                </g>
                                <g class="png" transform="matrix(0.8615599870681763,0,0,0.8615599870681763,27.62895965576172,81.04568481445312)" opacity="1" style="display: block;">
                                    <image width="168px" height="44px" preserveAspectRatio="xMidYMid slice" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAKgAAAAsBAMAAAADRO5JAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAJFBMVEXzwwBHcEzzwgDzwwD0wwDzwwD0xADzwwDzwwDxwQDzwwD0wwDcY0swAAAADHRSTlP/AD+/In+jY9wQT49dhnwkAAADuUlEQVRIx+WXz08TQRTHh/4GLky7tAUutNaq4QKCJtxaKFh7ag2NopdWSJVwKYJoPLWxAY0XNiTG6EUuJN6oF/8934+Z2S22tCQ1MXEOzM7uzGfevPd9r4OQA7fT7KAzxaATZ0p3d/alPVTox0ih/TQvW8OEXmxUzsql2e3iwkDQG4t9W8kKHK/lWrcPlta/1lrRvvOXRF30bb7p3XZeri6/zJ4mnj3tv8AnVpJ9W0pOHb0Prm5vFUL3m7L/gsSAPn1gn21nUjs/CvZQJSW3X7Srw9apXN3Lnw0deo32r0Ffl8tljtvNcvkxd9Q2RbqG7QDqBer2wFkztXJ+5Gbkbn1QFeW8RN08SNI8haArGp0qLe/IDPXHGnkIA6+DnGvAWEEbHgNt4oMlekBDNkOFKhk0wYGu0jeGhkXAQAkf6wUVVQXllXnRAY25PsmIELaGjuNDtCfUo6E+dSA3tOiGZpQrEUr4k27Q0CLuqaEhWyr/Guik+xCyIsSogWI+1B1okHMf3gTxtAEABbZwXhtCgf2vcw2dxeG9VwzFUwQNdIF30VAf65SgYbAqg6/W6PzoQm9CWu8UFPfYlPKNrT3oNVC0WXSHTrGlPjqKX8bZXt0soWebGCY01C/ldA8omBZk6Ax+z/Ak6XLpnhnV9annldujLugY+pShOfjTZOgURrSuBKhapGOPhpYIQcHmCXEp+tJIKsFQXJO9EGJE7kPWsn1x1CNmcdVI1m+gbfm9J3RMKiiEMlFBaMWk9gRCMiopYQcv65OhozSxOzSgofMa+g12sjU0gNEKZFleniJtBzMb6KcG9t2P33Ydn8I4aRwbp3gITnlANU/IaHiCk3tBjqHin9DxJPzxMRRdxrpyoFE6LkNRXj/T9AmgczBah0gb6DjWUyUpAPgZmleeELYDjVE8GBoVJoMBmlLJXrxS/FYDYwYaEssOlBOTobpCgJSU97HCdYVanKaeQoVijDkSOnTEinF+ztCKhlYJyntku0I/s6XaBqUWA81ohdBJhJISQqPsii5Qb01wmhpdf+mExgw0Zi5MfoJS4Q3KzuiPGEm1NBQriTLI05HvCI071zB7XjkW/NULupdxDNW/SQY63VBQnP0omUzjAoLOUrHpAQ0qx3kTjHnbAZXrCqp+U7FuLRA0Qi+6Q4FF0E8JXUOfdEBlmqBhRlPvIWiYXlyG0nUzBUlu5XK5h66CB6/vuIYreE+16LYKBRL7w1oti/eUDbhe1HbxklHjVv0vLmhXQAf4R+K6zfdXoL8B/7cyv853sCEAAAAASUVORK5CYII="></image>
                                </g>
                            </g>
                        </svg>
                </div>
                <div data-v-1690b988="" class="com__box" style="display: none;">
                    <div class="loading" data-v-1690b988="">
                        <div class="shape shape-1" data-v-1690b988=""></div>
                        <div class="shape shape-2" data-v-1690b988=""></div>
                        <div class="shape shape-3" data-v-1690b988=""></div>
                        <div class="shape shape-4" data-v-1690b988=""></div>
                    </div>
                </div>
            </div>
            <div data-v-1690b988="" class="skeleton-wrapper" style="display: none;">
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton__content">
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton-avatar van-skeleton-avatar--round"></div>
                    <div class="van-skeleton__content">
                        <h3 class="van-skeleton-title"></h3>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
                <div data-v-1690b988="" class="van-skeleton van-skeleton--animate">
                    <div class="van-skeleton__content">
                        <h3 class="van-skeleton-title"></h3>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 100%;"></div>
                        <div class="van-skeleton-paragraph" style="width: 60%;"></div>
                    </div>
                </div>
            </div>
        </div>
        <div data-v-9bebd08e="" class="content" style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
            <div data-v-58c5e826="" data-v-9bebd08e="" class="navbar">
                <div data-v-58c5e826="" class="navbar-fixed" style="background: rgb(63, 63, 63);">
                    <div data-v-58c5e826="" class="navbar__content">
                        <div data-v-58c5e826="" class="navbar__content-left">
                            <div data-v-9bebd08e="" class="logo" style="background-image: url(./index_files/h5setting_202401100608011fs2.png); width: 2.12rem; height: 2.12rem; background-repeat: no-repeat; background-size: cover; background-position: center; margin-top: 10px;"></div>

                            <!--<div data-v-9bebd08e="" class="languages" style="display:block; margin-top: 10px;">-->
                            <!--    <div data-v-187d09fa="" data-v-9bebd08e="">-->
                            <!--        <div data-v-187d09fa="" class="right">-->
                            <!--            <div data-v-187d09fa="" class="img">-->
                            <!--                <img data-v-187d09fa="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/en-4b649537.png" src="./index_files/en-4b649537.png">-->
                            <!--            </div>-->
                            <!--            <span data-v-187d09fa="" class="lang-text">EN</span>-->
                            <!--        </div>-->
                            <!--    </div>-->
                            <!--    <span data-v-9bebd08e="" class="languages-text"></span>-->
                            <!--</div>-->

                            <div data-v-9bebd08e="" class="languages" style="/* display:none; */margin-top: 10px;">
                                <div data-v-187d09fa="" data-v-9bebd08e="" style="
    color: #deb559;
    font-size: 14px;
    font-weight: 800;
">
                                    The Future of Gaming
                                </div>

                            </div>


                        </div>
                        <div data-v-58c5e826="" class="navbar__content-center">
                            <div data-v-58c5e826="" class="navbar__content-title"></div>
                        </div>
                        <div data-v-58c5e826="" class="navbar__content-right">
                            <div data-v-9bebd08e="" class="content__right">
                                <div data-v-9bebd08e="" class="btn" onclick="window.location='index_files/Globalwin.apk'">
                                    <svg data-v-9bebd08e="" xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 60 60" fill="none">
                                            <g data-v-9bebd08e="" clip-path="url(#clip0_243_123505)">
                                                <circle data-v-9bebd08e="" cx="30" cy="30" r="28.5" stroke="#3F3F3F" stroke-width="3"></circle>
                                                <path data-v-9bebd08e="" fill-rule="evenodd" clip-rule="evenodd" d="M29.9999 36.25L16.25 22.5543H25.4166V10H34.5833V22.5543H43.75L29.9999 36.25Z" fill="#3F3F3F" stroke="#3F3F3F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                                <path data-v-9bebd08e="" d="M50 41H10" stroke="#3F3F3F" stroke-width="3" stroke-linecap="round"></path>
                                                <path data-v-9bebd08e="" d="M41.25 48.5H18.75" stroke="#3F3F3F" stroke-width="3" stroke-linecap="round"></path>
                                            </g>
                                            <defs data-v-9bebd08e="">
                                                <clippath data-v-9bebd08e="" id="clip0_243_123505">
                                                    <rect data-v-9bebd08e="" width="60" height="60" fill="white"></rect>
                                                </clippath>
                                            </defs>
                                        </svg>
                                    <span data-v-9bebd08e="">Download APP</span>
                                </div>
                                <div data-v-9bebd08e="" class="btn" onclick="window.location='/promotion/socialCampaign'">

                                    <span data-v-9bebd08e="">🚀 Social Earn</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div data-v-747942a2="" data-v-9bebd08e="" class="swiper swiper-initialized swiper-horizontal swiper-pointer-events my-swipe swiper-backface-hidden">
                <div class="slideshow-container">
                    <div class="mySlides ">
                        <img src="./index_files/b-01.jpg" style="width:100%">
                    </div>
                    <div class="mySlides ">
                        <img src="./index_files/b-02.jpg" style="width:100%">
                    </div>
                    <div class="mySlides ">
                        <img src="./index_files/b-03.jpg" style="width:100%">
                    </div>
                    <div class="mySlides ">
                        <img src="./index_files/b-04.jpg" style="width:100%">
                    </div>
                </div>
                <br>
                <div style="text-align:center">
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                    <span class="dot"></span>
                </div>
            </div>
            <div data-v-2f7ed6fa="" data-v-9bebd08e="" class="noticeBar__container">
                <svg data-v-2f7ed6fa="" xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 48 48" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M41.977 36.5356L41.976 36.5376C41.488 37.7866 40.241 38.3286 39.189 37.7476C38.141 37.1596 37.692 35.6806 38.171 34.4346C39.25 31.7086 39.804 28.5196 39.804 25.1886C39.804 21.3406 39.04 17.6176 37.646 14.6966C37.077 13.4856 37.437 11.9706 38.44 11.2946C39.445 10.6176 40.732 11.0446 41.302 12.2386C43.071 15.9616 44.014 20.4326 43.999 25.1886C43.999 29.2326 43.295 33.1696 41.977 36.5356ZM34.32 32.2426C34.095 32.2426 33.87 32.1896 33.646 32.1006C32.554 31.6596 31.969 30.2506 32.34 28.9526L32.341 28.9476C32.686 27.7366 32.866 26.4716 32.866 25.1706C32.866 23.6386 32.627 22.1786 32.148 20.7886C31.713 19.5066 32.222 18.0636 33.3 17.5466C34.38 17.0476 35.592 17.6536 36.027 18.9366C36.702 20.9136 37.046 23.0156 37.046 25.1886C37.046 27.0416 36.793 28.8406 36.298 30.5686C35.998 31.6016 35.188 32.2426 34.32 32.2426ZM27.461 41.2976C26.664 41.7546 25.802 41.9976 24.923 41.9976C24.114 41.9976 23.317 41.8086 22.575 41.4046C22.535 41.3786 22.481 41.3516 22.44 41.3246L13.396 34.4406H10.278C7.364 34.4406 5 33.0836 5 30.1766V18.7326C5 15.8386 7.362 14.4826 10.264 14.4826H13.382L22.44 7.66557C22.481 7.63857 22.522 7.62557 22.561 7.59857C24.088 6.76357 25.945 6.80457 27.435 7.70657C29.027 8.66257 29.984 10.3716 30.012 12.2706V36.7336C30.012 38.6456 29.055 40.3546 27.461 41.2976Z" fill="url(#paint0_linear_235_31)"></path>
                        <defs>
                            <lineargradient id="paint0_linear_235_31" x1="5" y1="7" x2="5" y2="40.9996" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#FAE59F"></stop>
                                <stop offset="1" stop-color="#C4933F"></stop>
                            </lineargradient>
                        </defs>
                    </svg>
                <div data-v-2f7ed6fa="" class="noticeBar__container-body">
                    <div data-v-2f7ed6fa="" class="noticeBar__container-body-text">Welcome To&nbsp;<span id="domain"></span> !🎉</div>
                </div>
                <button data-v-2f7ed6fa="">
                        <svg data-v-2f7ed6fa="" xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <g clip-path="url(#clip0_235_37)">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M20.4481 16.0478C20.874 14.8905 21.0547 13.6797 20.9857 12.4515L20.9287 11.4448C20.9182 11.2994 20.7615 11.2118 20.6303 11.2798L19.723 11.7474C19.1553 12.0426 18.4527 12.2279 17.6444 12.2967C17.5904 12.3003 17.5544 12.28 17.5327 12.2627C17.5102 12.2453 17.4832 12.212 17.4809 12.1599C17.4764 12.1128 17.4952 12.0665 17.5297 12.0347C18.6754 10.9773 19.561 9.39667 20.1594 7.33113C20.6588 5.61661 20.6835 3.93176 20.2404 2.32001L19.9157 1.14539C19.8737 0.991232 19.6705 0.948532 19.5647 1.07157L18.7452 2.00518C18.1843 2.64207 17.5957 3.11684 16.9988 3.40922C16.2677 3.76675 15.4871 4.00124 14.675 4.1069C14.1051 4.17928 13.5353 4.19013 12.9751 4.13585C11.6217 4.00269 10.23 4.18145 8.95373 4.64826C7.6655 5.12158 6.4815 5.8967 5.5247 6.89183C4.67963 7.77261 4.03176 8.78656 3.5991 9.91197C3.18294 10.9968 2.98048 12.1374 3.00148 13.299C3.02473 14.4614 3.26542 15.5882 3.72208 16.647C4.19523 17.7442 4.87684 18.7198 5.75115 19.5499C6.62472 20.38 7.63551 21.0126 8.75727 21.4331C9.84229 21.8391 10.9851 22.0302 12.1548 21.9961C13.3231 21.9657 14.4606 21.7132 15.5298 21.2485C16.6396 20.7665 17.6287 20.0775 18.4752 19.1967C19.348 18.2877 20.0132 17.2289 20.4481 16.0478Z" fill="#292929"></path>
                            </g>
                            <defs>
                                <clippath id="clip0_235_37">
                                    <rect width="24" height="24" rx="10" fill="white"></rect>
                                </clippath>
                            </defs>
                        </svg>
                        Detail 
                    </button>
            </div>
        </div>

        <script>
            // Get the domain name from the current location
            const domainName = window.location.hostname;

            // Inject the domain name into the HTML elements
            document.getElementById('domain').innerText = domainName;
            // document.getElementById('domain-name').innerText = domainName;
        </script>
        <div class="balance-section">
            <div class="user-icons">
                <span class="material-symbols-outlined">person
</span>
            </div>
            <span>MemberNNG9SALA-</span>
            <span>₹0.00</span>
            <div class="email-icons" style="    float: right;
    margin-left: auto;">
                <span class="material-symbols-outlined">mail
</span>
            </div>
        </div>
        <!-- <div class="gamesList__section">
                <div data-v-a755d2fc="" class="menu_box1">
                    <div class="box" onclick="window.location='#lottery'">
                        <img src="/images/lottery.png">
                        <span>Game</span>
                    </div>
                    <div class="box" onclick="window.location='#aviator'" class="avio">
                        <img src="/images/original.png" onclick="openTab('tab5')">
                        <span>Aviator</span>
                    </div>
                    <div class="box"   onclick="window.location='#aviator'" class="avio">
                        <img src="/images/slot.png" onclick="openTab('tab5')">
                        <span>Slots</span>
                    </div>
                </div>
                <div class="menu_box2" onclick="window.location='#aviator'" class="avio">
                    <div class="box"  onclick="openTab('tab5')">
                        <img src="/images/sport.png">
                        <span>Sport</span>
                    </div>
                    <div class="box"   onclick="window.location='#aviator'" class="avio">
                        <img src="/images/popular.png" onclick="openTab('tab5')">
                        <span>Popular</span>
                    </div>
                    <div class="box"    onclick="window.location='#aviator'" class="avio">
                        <img src="/images/casino.png"  onclick="openTab('tab5')">
                        <span>Casino</span>
                    </div>
                </div>
                <div class="menu_box3"  onclick="window.location='#aviator'" class="avio">
                    <div class="box">
                        <img src="/images/rumy.png" onclick="openTab('tab5')">
                        <span>Rummy</span>
                    </div>
                    <div class="box"  onclick="window.location='#aviator'" class="avio">
                        <img src="/images/fishing.png"  onclick="openTab('tab5')">
                        <span>Fishing</span>
                    </div>
                </div>
            </div> -->
        <div class="game-tabs-section" id="lottery">
            <h3 id="h3">Game</h3>
            <div class="tabs">
                <button onclick="openTab('tab1')">All</button>
                <button onclick="openTab('tab2')">Win Go</button>
                <button onclick="openTab('tab3')">K3</button>
                <button onclick="openTab('tab4')">5D</button>
                <button onclick="openTab('tab5')" id="aviator">Aviator</button>
            </div>
            <div id="tab1" class="tab-content active">
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/10'">
                    <span data-v-f0612e6a="">Win Go 30Sec</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win'">
                    <span data-v-f0612e6a="">Win Go 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/3'">
                    <span data-v-f0612e6a="">Win Go 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/5'">
                    <span data-v-f0612e6a="">Win Go 5Min</span>
                </div>



                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 10Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 10Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" style="border-radius: 5px;" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/aviator.png" onclick="window.location='/aviator'">
                    <span data-v-f0612e6a="">Aviator</span>
                </div>
            </div>
            <div id="tab2" class="tab-content">
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win'">
                    <span data-v-f0612e6a="">Win Go 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/3'">
                    <span data-v-f0612e6a="">Win Go 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/5'">
                    <span data-v-f0612e6a="">Win Go 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062051do1k.png" src="./index_files/lotterycategory_20240110062051do1k.png" onclick="window.location='/win/10'">
                    <span data-v-f0612e6a="">Win Go 10Min</span>
                </div>
            </div>
            <div id="tab6" class="tab-content">
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="index_files/trx.jpeg" src="./index_files/trx.jpeg" onclick="window.location='/trx'">
                    <span data-v-f0612e6a="">TRX 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="index_files/trx.jpeg" src="./index_files/trx.jpeg" onclick="window.location='/trx/3'">
                    <span data-v-f0612e6a="">TRX 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="index_files/trx.jpeg" src="./index_files/trx.jpeg" onclick="window.location='/trx/5'">
                    <span data-v-f0612e6a="">TRX 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="index_files/trx.jpeg" src="./index_files/trx.jpeg" onclick="window.location='/trx/10'">
                    <span data-v-f0612e6a="">TRX 10Min</span>
                </div>
            </div>
            <div id="tab3" class="tab-content">
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/lotterycategory_20240110062111bt8e.png" onclick="window.location='/k3'">
                    <span data-v-f0612e6a="">K3 10Min</span>
                </div>
            </div>
            <div id="tab4" class="tab-content">
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 1Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 3Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 5Min</span>
                </div>
                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062118e9kt.png" src="./index_files/lotterycategory_20240110062118e9kt.png" onclick="window.location='/5d'">
                    <span data-v-f0612e6a="">5D 10Min</span>
                </div>
            </div>
            <div id="tab5" class="tab-content">

                <div data-v-f0612e6a="" class="lotterySlotItem">
                    <img data-v-f0612e6a="" class="" data-origin="https://ossimg.bdgadminbdg.com/IndiaBDG/lotterycategory/lotterycategory_20240110062111bt8e.png" src="./index_files/2.png" onclick="window.location='/aviator'">
                    <span data-v-f0612e6a=""></span>
                </div>

            </div>
            <div data-v-84210c68="" data-v-9bebd08e="" class="luckyWinners__container">
                <h1 data-v-84210c68="">Winning information</h1>
                <div data-v-84210c68="" class="luckyWinners__container-wrapper">
                    <div data-v-84210c68="" style="position: relative;">
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" src="/assets/png/8-ea087ede.png" class="ar-lazyload" data-origin="/assets/png/16-cf8e1441.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***DAU</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹160.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" src="/assets/png/avatar-fa220f51.png" class="ar-lazyload" data-origin="/assets/png/1-c7e4efc8.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***YAB</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹510.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" src="https://www.bigdaddygame2.com/assets/png/1-c7e4efc8.png" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/6-7c7f5203.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***DZP</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹3,020.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" src="/assets/png/4-12a0d0c5.png" class="ar-lazyload" data-origin="/assets/png/19-2ac9fd83.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***NBJ</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹120.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" src="/assets/png/16-cf8e1441.png" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/8-ea087ede.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***NGX</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹120.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                        <div data-v-84210c68="" class="luckyWinners__container-wrapper__item">
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-img">
                                <img data-v-84210c68="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/10-29a6603e.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-info">
                                <h1 data-v-84210c68="">Mem***SIZ</h1>
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winType">
                                <img data-v-84210c68="" class="ar-lazyload" data-origin="https://www.bigdaddygame2.com/assets/png/JILI-751836f3.png" src="/index_files/WinGo-f48e5435.png">
                            </div>
                            <div data-v-84210c68="" class="luckyWinners__container-wrapper__item-winAmount">
                                <h1 data-v-84210c68="">Receive ₹180.00</h1>
                                <span data-v-84210c68="">Winning amount</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div data-v-c310f7df="" data-v-9bebd08e="" class="dailyProfitRank">
                <h1 data-v-c310f7df="">Today's earnings chart</h1>
                <div data-v-c310f7df="" class="dailyProfitRank__content">
                    <div data-v-c310f7df="" class="dailyProfitRank__content-topThree">
                        <div data-v-c310f7df="" class="dailyProfitRank__content-topThree__item" style="order: 2; top: -45px;">
                            <div data-v-c310f7df="" style="background: url(&quot;/assets/png/border1-3b6518ec.png&quot;) center center / 100% 100% no-repeat;">
                                <img data-v-c310f7df="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="" data-origin="https://www.bigdaddygame2.com/assets/png/6-7c7f5203.png" src="./index_files/6-7c7f5203.png">
                            </div>
                            <div data-v-c310f7df="">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/crown1-7e4e2fd9.png" src="./index_files/crown1-7e4e2fd9.png">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/place1-c2ee5099.png" src="./index_files/place1-c2ee5099.png">
                            </div>
                            <span data-v-c310f7df="">Mem***SJE</span>
                            <span data-v-c310f7df="">₹153,076,0.00</span>
                        </div>
                        <div data-v-c310f7df="" class="dailyProfitRank__content-topThree__item" style="order: 1; top: -20px;">
                            <div data-v-c310f7df="" style="background: url(&quot;/assets/png/border2-7a806be7.png&quot;) center center / 100% 100% no-repeat;">
                                <img data-v-c310f7df="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="" data-origin="https://www.bigdaddygame2.com/assets/png/17-bedde42f.png" src="./index_files/17-bedde42f.png">
                            </div>
                            <div data-v-c310f7df="">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/crown2-8b009e96.png" src="./index_files/crown2-8b009e96.png">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/place2-2e2fac0d.png" src="./index_files/place2-2e2fac0d.png">
                            </div>
                            <span data-v-c310f7df="">Mem***RAP</span>
                            <span data-v-c310f7df="">₹125,744.60</span>
                        </div>
                        <div data-v-c310f7df="" class="dailyProfitRank__content-topThree__item" style="order: 3; top: -20px;">
                            <div data-v-c310f7df="" style="background: url(&quot;/assets/png/border3-cfec4a7d.png&quot;) center center / 100% 100% no-repeat;">
                                <img data-v-c310f7df="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="" data-origin="https://www.bigdaddygame2.com/assets/png/3-abfcc056.png" src="./index_files/3-abfcc056.png">
                            </div>
                            <div data-v-c310f7df="">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/crown3-2ca02146.png" src="./index_files/crown3-2ca02146.png">
                                <img data-v-c310f7df="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/place3-d9b0be38.png" src="./index_files/place3-d9b0be38.png">
                            </div>
                            <span data-v-c310f7df="">Mem***FEW</span>
                            <span data-v-c310f7df="">₹62,667.00</span>
                        </div>
                    </div>
                    <div data-v-c310f7df="" class="dailyProfitRank__content-list">
                        <div data-v-c310f7df="" class="dailyProfitRank__content-list__item">
                            <span data-v-c310f7df="" class="left-rank">4</span>
                            <img data-v-c310f7df="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="" data-origin="https://www.bigdaddygame2.com/assets/png/19-2ac9fd83.png" src="./index_files/19-2ac9fd83.png">
                            <span data-v-c310f7df="" class="middle-name">Mem***JHT</span>
                            <span data-v-c310f7df="" class="right-box">₹30,825.20</span>
                        </div>
                        <div data-v-c310f7df="" class="dailyProfitRank__content-list__item">
                            <span data-v-c310f7df="" class="left-rank">5</span>
                            <img data-v-c310f7df="" data-img="https://www.bigdaddygame2.com/assets/png/avatar-fa220f51.png" class="" data-origin="https://www.bigdaddygame2.com/assets/png/7-00479cfa.png" src="./index_files/7-00479cfa.png">
                            <span data-v-c310f7df="" class="middle-name">Mem***AGB</span>
                            <span data-v-c310f7df="" class="right-box">₹12,679.56</span>
                        </div>
                    </div>
                </div>
            </div>
            <div data-v-d6ad76e9="" data-v-9bebd08e="" class="dialog inactive">
                <div data-v-d6ad76e9="" class="dialog__container" role="dialog" tabindex="0">
                    <div data-v-d6ad76e9="" class="dialog__container-img">
                        <img data-v-d6ad76e9="" alt="" class="" data-origin="https://www.bigdaddygame2.com/assets/png/superjackpotHome-72bbeb43.png" src="./index_files/superjackpotHome-72bbeb43.png">
                    </div>
                    <div data-v-d6ad76e9="" class="dialog__container-title">
                        <h1 data-v-d6ad76e9="">Congratulation</h1>
                    </div>
                    <div data-v-d6ad76e9="" class="dialog__container-content">
                        <div data-v-9bebd08e="" class="Laundry-Con">
                            <div data-v-9bebd08e="" class="Laundry-Con_tip">Get 【Super Jackpot】!</div>
                            <div data-v-9bebd08e="" class="Landty-Con-tips">Visit the [Super Jackpot] page to claim your reward</div>
                        </div>
                    </div>
                    <div data-v-d6ad76e9="" class="dialog__container-footer">
                        <button data-v-d6ad76e9="">OK</button>
                    </div>
                </div>
                <div data-v-d6ad76e9="" class="dialog__outside"></div>
            </div>
        </div>
        <div class="customer" id="customerId" style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif; --17a7a9f6: bahnschrift;" onclick="window.location='/keFuMenu'">
            <img class="" data-origin="https://www.bigdaddygame2.com/assets/png/icon_sevice-1ca64bcf.png" src="./index_files/icon_sevice-1ca64bcf.png">
        </div>
        <div data-v-76c247f8="" class="tabbar__container" style="--7688837a: &#39;Roboto&#39;, &#39;Inter&#39;, sans-serif;">
            <div data-v-76c247f8="" class="tabbar__container-item" onclick="window.location='/home'">
                <svg data-v-76c247f8="" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none" class="">
                    <g clip-path="url(#clip0_226_251)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M52.3509 27.9323L29.2711 5.07261C28.5724 4.37977 27.4394 4.37847 26.74 5.07065L26.7387 5.07261L3.65824 27.931C2.98428 28.5985 2.60466 29.5042 2.604 30.4484C2.60596 32.411 4.21107 34.0005 6.19061 34.0018H8.6227V50.3141C8.62075 51.2941 9.42168 52.0898 10.4108 52.0911C10.4121 52.0911 10.4141 52.0911 10.416 52.0911H24.4186V39.65H30.6945V52.0911H45.5925C46.5816 52.0924 47.3838 51.2993 47.3858 50.3186C47.3858 50.3173 47.3858 50.3154 47.3858 50.3141V34.0018H49.8172C51.8 33.9966 53.4032 32.3987 53.3973 30.4334C53.3947 29.4951 53.0183 28.5959 52.3496 27.931L52.3509 27.9323Z" fill="url(#paint0_linear_226_251)"></path>
                    </g>
                    <defs>
                        <lineargradient id="paint0_linear_226_251" x1="2.604" y1="52.0931" x2="2.604" y2="4.55225" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#6F7381"></stop>
                            <stop offset="1" stop-color="#A9AAB5"></stop>
                        </lineargradient>
                        <lineargradient id="paint0_linear_226_233" x1="6.5116" y1="52.7442" x2="6.5116" y2="3.90698" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C4933F"></stop>
                            <stop offset="1" stop-color="#FAE59F"></stop>
                        </lineargradient>
                        <clippath id="clip0_226_251">
                            <rect width="56" height="56" fill="white"></rect>
                        </clippath>
                    </defs>
                </svg>
                <span data-v-76c247f8="" onclick="window.location='/home'">Home</span>
            </div>
            <div data-v-76c247f8="" class="tabbar__container-item" onclick="window.location='/checkIn'">
                <svg data-v-76c247f8="" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none" class="">
                    <g clip-path="url(#clip0_226_254)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M44.182 30.186C41.748 35.6375 36.5016 39.6076 30.2276 40.2914V48.3554H36.3668C37.5955 48.3554 38.6009 49.3412 38.6009 50.5472C38.6009 51.7538 37.5955 52.7396 36.3668 52.7396H19.6234C18.396 52.7396 17.3906 51.7538 17.3906 50.5472C17.3906 49.3412 18.396 48.3554 19.6234 48.3554H25.7619V40.223C19.7836 39.3569 14.8048 35.4838 12.4515 30.2133C7.01041 30.1039 2.60074 25.719 2.60074 20.3475C2.60074 15.4697 6.25572 11.3862 11.0001 10.6192V7.05601C11.0001 5.30242 12.4515 3.90503 14.2103 3.90503H42.3966C44.182 3.90503 45.6035 5.33042 45.6035 7.05601V10.7559C50.0412 11.7698 53.3908 15.6885 53.3908 20.3475C53.3908 25.5002 49.3158 29.7751 44.182 30.186ZM11.0001 23.6913V15.1135C8.73925 15.7985 7.06576 17.8809 7.06576 20.3475C7.06576 22.8695 8.79655 25.006 11.1401 25.6096C11.0561 24.9786 11.0001 24.3483 11.0001 23.6913ZM35.7827 17.7989L31.317 17.1959L29.3336 13.0584C28.5815 11.6057 27.4647 11.6057 26.8513 13.0584L24.8692 17.0865L20.4048 17.6895C18.8987 17.9363 18.5353 19.0322 19.6514 20.1288L22.8884 23.4165L22.1337 28.0488C21.8843 29.611 22.749 30.2407 24.1171 29.5009L28.1068 27.3085L32.0971 29.6377C33.4646 30.3774 34.3299 29.7751 34.0793 28.1856L33.3259 23.5532L36.5628 20.2655C37.6795 19.1696 37.3168 18.073 35.7827 17.7989ZM45.6035 15.3603V23.7186C45.6035 24.2936 45.5488 24.8419 45.4941 25.4175C47.5017 24.5951 48.9251 22.6221 48.9251 20.3749C48.9251 18.1277 47.5577 16.2094 45.6035 15.3603Z" fill="url(#paint0_linear_226_254)"></path>
                    </g>
                    <defs>
                        <lineargradient id="paint0_linear_226_254" x1="2.60074" y1="52.7442" x2="2.60074" y2="3.90503" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#6F7381"></stop>
                            <stop offset="1" stop-color="#A9AAB5"></stop>
                        </lineargradient>
                        <lineargradient id="paint0_linear_226_233" x1="6.5116" y1="52.7442" x2="6.5116" y2="3.90698" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C4933F"></stop>
                            <stop offset="1" stop-color="#FAE59F"></stop>
                        </lineargradient>
                        <clippath id="clip0_226_254">
                            <rect width="56" height="56" fill="white"></rect>
                        </clippath>
                    </defs>
                </svg>
                <span data-v-76c247f8="" onclick="window.location='/checkIn'">Commission</span>
            </div>
            <div data-v-76c247f8="" class="tabbar__container-item" onclick="window.location='/promotion'">
                <svg data-v-76c247f8="" width="140" height="140" viewBox="0 0 140 140" fill="none" xmlns="http://www.w3.org/2000/svg" class="">
                    <g clip-path="url(#clip0_610_5412)">
                        <g clip-path="url(#clip1_610_5412)">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M112.494 16C115.5 16 118.181 17.5313 119.725 20.1104L133.863 44.1284C135.569 47.1105 135.325 57.1851 133.213 59.8448L79.8313 119.325C75.525 124.645 67.6438 125.531 62.2 121.26C61.4688 120.696 60.8188 120.051 60.25 119.325L6.78751 59.8448C4.59376 57.1851 4.35001 47.0299 6.13751 44.1284L20.275 20.1104C21.8188 17.5313 24.5813 16 27.5063 16H112.494Z" fill="#3B3B3B"></path>
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M103.637 31.3135C105.994 31.3135 108.106 32.4418 109.244 34.3762L120.294 52.2687C121.675 54.4448 121.431 57.2657 119.725 59.2L77.9625 106.833C74.55 110.782 68.4562 111.427 64.2312 108.284C63.6625 107.881 63.175 107.397 62.6875 106.833L21.0875 59.2C19.3813 57.1851 19.2188 54.4448 20.5188 52.2687L31.5688 34.3762C32.7063 32.5224 34.9 31.3135 37.175 31.3135H103.637ZM82.7562 68.6299L72.6 83.1374C72.1125 83.8627 70.2437 84.0239 69.5125 83.4597C69.35 83.3791 69.2687 83.218 69.1062 83.1374L58.1375 68.6299C56.5938 66.5344 53.5062 66.0508 51.3125 67.5821C49.1187 69.0329 48.6312 71.9344 50.175 74.0299L61.1437 88.5374C65.3687 93.6956 68.05 95.7911 70.8937 95.7911C74.0625 95.7911 76.7437 92.2448 79.75 88.5374L90.7187 74.0299C92.2625 71.9344 91.775 69.0329 89.5812 67.5821C87.3875 66.0508 84.3 66.5344 82.7562 68.6299Z" fill="url(#paint0_linear_610_5412)"></path>
                        </g>
                    </g>
                    <defs>
                        <lineargradient id="paint0_linear_610_5412" x1="70.4231" y1="31.3135" x2="70.4231" y2="110.276" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#FAE59F"></stop>
                            <stop offset="1" stop-color="#C4933F"></stop>
                        </lineargradient>
                        <clippath id="clip0_610_5412">
                            <rect width="140" height="140" fill="white"></rect>
                        </clippath>
                        <clippath id="clip1_610_5412">
                            <rect width="130" height="108" fill="white" transform="translate(5 16)"></rect>
                        </clippath>
                    </defs>
                </svg>
                <span data-v-76c247f8="" onclick="window.location='/promotion'">Promotion</span>
            </div>
            <div data-v-76c247f8="" class="tabbar__container-item" onclick="window.location='/wallet'">
                <svg data-v-76c247f8="" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none" class="wallet">
                    <g clip-path="url(#clip0_226_268)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M49.4884 48.8372H13.6744C11.3784 48.8372 9.48486 47.1383 9.16775 44.9302H46.2326C48.3905 44.9302 50.1396 43.1812 50.1396 41.0233V14.3777C52.3476 14.6942 54.0465 16.5877 54.0465 18.8837V44.2791C54.0465 46.7965 52.0058 48.8372 49.4884 48.8372ZM42.3256 41.6744H7.16281C4.64542 41.6744 2.60468 39.6337 2.60468 37.1163V12.3721C2.60468 9.85471 4.64542 7.81396 7.16281 7.81396H42.3256C44.843 7.81396 46.8837 9.85471 46.8837 12.3721V37.1163C46.8837 39.6337 44.843 41.6744 42.3256 41.6744ZM10.8158 32.5621H35.1439C37.3116 32.5621 39.0691 31.8113 39.0691 29.7054C39.0691 29.4436 38.853 29.234 38.5866 29.234H18.3837C17.6791 29.234 17.1087 28.6798 17.1087 27.9955V25.6734C17.1087 25.5184 16.9804 25.3921 16.8209 25.3941C15.3134 25.409 14.7267 26.3487 13.0793 28.5502L10.4186 32.1766C10.4186 32.3888 10.5977 32.5621 10.8158 32.5621ZM38.6719 16.9257H14.3445C12.1761 16.9257 10.4193 17.6771 10.4193 19.783C10.4193 20.0448 10.6348 20.2544 10.9018 20.2544H31.1048C31.8087 20.2544 32.3797 20.8086 32.3797 21.4923V23.8143C32.3797 23.97 32.5074 24.0956 32.6676 24.0943C34.175 24.0794 34.7617 23.1397 36.4091 20.9382L39.0698 17.3118C39.0698 17.0995 38.8907 16.9257 38.6719 16.9257Z" fill="url(#paint0_linear_226_268)"></path>
                    </g>
                    <defs>
                        <lineargradient id="paint0_linear_226_268" x1="2.60468" y1="48.8372" x2="2.60468" y2="7.81396" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#6F7381"></stop>
                            <stop offset="1" stop-color="#A9AAB5"></stop>
                        </lineargradient>
                        <lineargradient id="paint0_linear_226_233" x1="6.5116" y1="52.7442" x2="6.5116" y2="3.90698" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C4933F"></stop>
                            <stop offset="1" stop-color="#FAE59F"></stop>
                        </lineargradient>
                        <clippath id="clip0_226_268">
                            <rect width="56" height="56" fill="white"></rect>
                        </clippath>
                    </defs>
                </svg>
                <span data-v-76c247f8="" onclick="window.location='/wallet'">Wallet</span>
            </div>
            <div data-v-76c247f8="" class="tabbar__container-item" onclick="window.location='/mian'">
                <svg data-v-76c247f8="" xmlns="http://www.w3.org/2000/svg" width="56" height="56" viewBox="0 0 56 56" fill="none" class="">
                    <g clip-path="url(#clip0_226_262)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M23.0837 30.8723C13.9446 30.8723 6.5116 38.149 6.5116 47.1012V48.0656C6.5116 52.7442 13.8261 52.7442 23.0837 52.7442H32.9162C41.8098 52.7442 49.4883 52.7442 49.4883 48.0656V47.1012C49.4883 38.1536 42.0547 30.8723 32.9162 30.8723H23.0811H23.0837ZM27.507 29.6318C34.7447 29.6318 40.6312 23.8638 40.6312 16.7707C40.6312 9.67694 34.7447 3.90698 27.5096 3.90698C20.2713 3.90698 14.3822 9.67694 14.3822 16.7707C14.3822 23.8619 20.2713 29.6318 27.5096 29.6318H27.507Z" fill="url(#paint0_linear_226_262)"></path>
                    </g>
                    <defs>
                        <lineargradient id="paint0_linear_226_262" x1="6.5116" y1="52.7442" x2="6.5116" y2="3.90698" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#6F7381"></stop>
                            <stop offset="1" stop-color="#A9AAB5"></stop>
                        </lineargradient>
                        <lineargradient id="paint0_linear_226_233" x1="6.5116" y1="52.7442" x2="6.5116" y2="3.90698" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#C4933F"></stop>
                            <stop offset="1" stop-color="#FAE59F"></stop>
                        </lineargradient>
                        <clippath id="clip0_226_262">
                            <rect width="56" height="56" fill="white"></rect>
                        </clippath>
                    </defs>
                </svg>
                <span data-v-76c247f8="" onclick="window.location='/mian'">Account</span>
            </div>
        </div>
    </div>
    <script>
        function generateRandomAmount() {
            // Generate a random amount between 1 and 5000 (you can adjust the range as needed)
            return "₹" + (Math.floor(Math.random() * 5000) + 1).toFixed(2);
        }

        // Function to generate a random user name
        function generateRandomUserName() {
            var names = ['mem', 'mem', 'mem', 'mem', 'mem', 'mem', 'mem', 'mem', 'mem', 'mem'];
            var randomIndex = Math.floor(Math.random() * names.length);
            var randomName = names[randomIndex];
            var randomInitials = Math.floor(Math.random() * 10000);
            // Adding random numbers for uniqueness
            return randomName + '***' + randomInitials;
        }

        // Function to generate a random image URL
        function generateRandomImageURL() {
            var images = ['/assets/png/8-ea087ede.png', '/assets/png/avatar-fa220f51.png', 'https://www.bigdaddygame2.com/assets/png/1-c7e4efc8.png', '/assets/png/4-12a0d0c5.png', '/assets/png/16-cf8e1441.png'];
            return images[Math.floor(Math.random() * images.length)];
        }

        // Function to update winning amounts, user names, and images
        function updateWinningInformation() {
            // Select all elements containing winning amounts, user names, and images
            var winAmountElements = document.querySelectorAll('.luckyWinners__container-wrapper__item-winAmount h1');
            var userNameElements = document.querySelectorAll('.luckyWinners__container-wrapper__item-info h1');
            var imageElements = document.querySelectorAll('.luckyWinners__container-wrapper__item-img img');

            // Loop through each element and update its content
            for (var i = 0; i < winAmountElements.length; i++) {
                winAmountElements[i].textContent = "Receive " + generateRandomAmount();
                userNameElements[i].textContent = generateRandomUserName();
                imageElements[i].setAttribute('src', generateRandomImageURL());
            }
        }

        // Call the updateWinningInformation function every 2 seconds
        setInterval(updateWinningInformation, 2000);
    </script>
    <script>
        const closeButton = document.getElementById('closeButton');
        const popUp = document.getElementById('containerWrapper');
        closeButton.addEventListener('click', function() {
            popUp.style.display = 'none';
            // Hide the pop-up when close button is clicked
        });
    </script>
    <script>
        $(document).ready(function() {
            // Select the overlay element
            $('.van-overlay').fadeIn(10);
        });
        $(window).on('load', function() {
            // Select the overlay element
            $('.van-overlay').fadeIn(10);
            setTimeout(function() {
                $('.van-overlay').fadeOut(300);
            }, 500);
        });
    </script>
    <script>
        // Define the URL
        const url = 'https://betpride.io/api/webapi/GetUserInfo';

        // Send a GET request using fetch
        fetch(url).then(response => {
            // Check if response is successful
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            // Parse the JSON response
            return response.json();
        }).then(data => {
            // Display the response data
            console.log(data);
            // Access and display the totalRecharge value
            console.log('totalRecharge:', data.totalRecharge);
            const totalRecharge = data.totalRecharge;

            // Array of total values
            const totals = [200000, 100000, 28000, 12000, 6000, 3000, 300];

            // Function to fetch data from the API and update progress bars
            function updateProgressBars(total, progressBarId, progressTextId) {
                let progressBar = document.getElementById(progressBarId);
                let progressText = document.getElementById(progressTextId);
                let progress = 0;
                let intervalMs = 100;

                function updateProgress() {
                    let percentage = (progress / total) * 100;
                    progressBar.style.width = percentage + '%';
                    progressText.textContent = progress + '/' + total;
                }

                let interval = setInterval(() => {
                    if (progress >= totalRecharge) {
                        clearInterval(interval);
                    }

                    updateProgress();
                    progress += 10;
                }, intervalMs);
            }

            // Iterate over the totals array and update progress bars
            for (let i = 0; i < totals.length; i++) {
                updateProgressBars(totals[i], 'progressBar' + i, 'progressText' + i);
            }
        }).catch(error => {
            // Handle any errors
            console.error('There was a problem with the fetch operation:', error);
        });
    </script>
    <script>
        let slideIndex = 0;
        showSlides();

        function showSlides() {
            let i;
            let slides = document.getElementsByClassName("mySlides");
            let dots = document.getElementsByClassName("dot");
            for (i = 0; i < slides.length; i++) {
                slides[i].style.display = "none";
            }
            slideIndex++;
            if (slideIndex > slides.length) {
                slideIndex = 1
            }
            for (i = 0; i < dots.length; i++) {
                dots[i].className = dots[i].className.replace(" active", "");
            }
            slides[slideIndex - 1].style.display = "block";
            dots[slideIndex - 1].className += " active";
            setTimeout(showSlides, 5000);
            // Change image every 5 seconds
        }
    </script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
    <script>
        document.querySelector('.van-button__text').addEventListener('click', function() {
            document.querySelector('.van-popup').style.display = 'none';
        });

        function openPopup() {
            document.getElementById('popup').style.display = 'block';
        }

        // Function to close the popup
        function closePopup() {
            document.getElementById('popup').style.display = 'none';
        }

        // Automatically open the popup when the page loads
        window.onload = function() {
            openPopup();
        };

        function openTab(tabId) {
            // Get all tab content elements
            let tabContents = document.querySelectorAll('.tab-content');

            // Hide all tab content
            tabContents.forEach(tabContent => tabContent.classList.remove('active'));

            // Show the selected tab content
            document.getElementById(tabId).classList.add('active');

        }
    </script>
</body>

</html>