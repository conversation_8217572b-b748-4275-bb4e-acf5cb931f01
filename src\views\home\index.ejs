<!DOCTYPE html>
<html lang="en" translate="no" data-dpr="1" style="font-size: 40.5px;">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Foontawsome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">

    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <link rel="stylesheet" crossorigin="" href="./index_files/index-BUBUniRp.css">
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous" />
    <meta name="google" content="notranslate">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Home</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Swiper CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />

    <style>
        /* Custom CSS for slider, popups and enhancements */
        
        .swiper {
            width: 100%;
            height: 100%;
        }
        
        .swiper-slide {
            text-align: center;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .swiper-slide img {
            display: block;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .swiper-pagination-bullet {
            width: 12px;
            height: 12px;
            background: #d9d9d9;
            opacity: 1;
        }
        
        .swiper-pagination-bullet-active {
            background: var(--main-color);
        }
        /* Popup Styling */
        
        .popup-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }
        
        .popup-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        .popup-content {
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            border-radius: 16px;
            width: 90%;
            max-width: 500px;
            padding: 30px;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            border: 2px solid var(--main-color);
        }
        
        .close-popup {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 24px;
            color: #fff;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .close-popup:hover {
            color: var(--main-color);
            transform: scale(1.1);
        }
        
        .popup-title {
            color: var(--main-color);
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .popup-text {
            color: #e6e6e6;
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }
        
        .popup-btn {
            background: var(--main-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 12px 30px;
            font-weight: bold;
            display: block;
            margin: 0 auto;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .popup-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
        /* Notification Popup */
        
        .notification-popup {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #1a1a2e;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            border-left: 4px solid var(--main-color);
            z-index: 10000;
            transform: translateX(120%);
            transition: transform 0.4s ease;
        }
        
        .notification-popup.show {
            transform: translateX(0);
        }
        /* Game Card Animation */
        
        .jili_games_card {
            display: block;
            overflow: hidden;
            border-radius: 12px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        
        .jili_games_card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .jili_games_card img {
            transition: transform 0.3s ease;
        }
        
        .jili_games_card:hover img {
            transform: scale(1.05);
        }
        /* Banner Animation */
        
        .swiper-slide {
            transition: transform 0.5s ease;
        }
        
        .swiper-slide-active {
            transform: scale(1.05);
            z-index: 1;
        }
        /* Active Nav Item */
        
        .tabbar__container-item.active {
            position: relative;
            color: var(--main-color);
        }
        
        .tabbar__container-item.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 6px;
            height: 6px;
            background: var(--main-color);
            border-radius: 50%;
        }
        /* Bottom Bar Animation */
        
        .bottombar {
            animation: glow 2s infinite alternate;
        }
        
        @keyframes glow {
            from {
                box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
            }
            to {
                box-shadow: 0 0 20px var(--main-color);
            }
        }
        
        .card {
            padding: 6px 5px;
        }
        /* Category Card Hover */
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        /* Responsive Adjustments */
        
        @media (max-width: 768px) {
            .swiper-slide {
                height: 200px;
            }
            .popup-content {
                width: 95%;
                padding: 20px;
            }
        }
        /* Welcome Blast Animation Styles */
        
        .welcome-blast-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 10000;
            pointer-events: none;
            overflow: hidden;
        }
        
        .blast-wave {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 0;
            height: 0;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(255, 165, 0, 0.6) 30%, rgba(255, 69, 0, 0.4) 60%, transparent 100%);
            animation: blastWave 2s ease-out forwards;
        }
        
        .blast-wave:nth-child(2) {
            background: radial-gradient(circle, rgba(0, 255, 255, 0.6) 0%, rgba(0, 191, 255, 0.4) 30%, rgba(30, 144, 255, 0.3) 60%, transparent 100%);
            animation-delay: 0.3s;
        }
        
        .blast-wave:nth-child(3) {
            background: radial-gradient(circle, rgba(255, 20, 147, 0.6) 0%, rgba(255, 105, 180, 0.4) 30%, rgba(255, 182, 193, 0.3) 60%, transparent 100%);
            animation-delay: 0.6s;
        }
        
        .blast-particles {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        .particle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #FFD700;
            border-radius: 50%;
            animation: particleBlast 2.5s ease-out forwards;
        }
        
        .welcome-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #FFD700;
            font-size: 2.5rem;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            opacity: 0;
            animation: welcomeTextAppear 1s ease-out 1.5s forwards;
            z-index: 10001;
        }
        
        @keyframes blastWave {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
            }
            50% {
                opacity: 0.8;
            }
            100% {
                width: 150vw;
                height: 150vw;
                opacity: 0;
            }
        }
        
        @keyframes particleBlast {
            0% {
                transform: translate(-50%, -50%) scale(1);
                opacity: 1;
            }
            100% {
                transform: translate(-50%, -50%) scale(0);
                opacity: 0;
            }
        }
        
        @keyframes welcomeTextAppear {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.5);
            }
            50% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1.2);
            }
            100% {
                opacity: 1;
                transform: translate(-50%, -50%) scale(1);
            }
        }
        
        .fade-out-animation {
            animation: fadeOut 1s ease-out forwards;
        }
        
        @keyframes fadeOut {
            0% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                visibility: hidden;
            }
        }
        /* Sparkle effects */
        
        .sparkle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: #FFD700;
            border-radius: 50%;
            animation: sparkleAnimation 3s ease-out forwards;
        }
        
        @keyframes sparkleAnimation {
            0% {
                opacity: 1;
                transform: scale(0) rotate(0deg);
            }
            50% {
                opacity: 1;
                transform: scale(1) rotate(180deg);
            }
            100% {
                opacity: 0;
                transform: scale(0) rotate(360deg);
            }
        }
    </style>

    <body cz-shortcut-listen="true">
        <style id="_goober">
            @keyframes go2264125279 {
                from {
                    transform: scale(0) rotate(45deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(45deg);
                    opacity: 1;
                }
            }
            
            @keyframes go3020080000 {
                from {
                    transform: scale(0);
                    opacity: 0;
                }
                to {
                    transform: scale(1);
                    opacity: 1;
                }
            }
            
            @keyframes go463499852 {
                from {
                    transform: scale(0) rotate(90deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(90deg);
                    opacity: 1;
                }
            }
            
            @keyframes go1268368563 {
                from {
                    transform: rotate(0deg);
                }
                to {
                    transform: rotate(360deg);
                }
            }
            
            @keyframes go1310225428 {
                from {
                    transform: scale(0) rotate(45deg);
                    opacity: 0;
                }
                to {
                    transform: scale(1) rotate(45deg);
                    opacity: 1;
                }
            }
            
            @keyframes go651618207 {
                0% {
                    height: 0;
                    width: 0;
                    opacity: 0;
                }
                40% {
                    height: 0;
                    width: 6px;
                    opacity: 1;
                }
                100% {
                    opacity: 1;
                    height: 10px;
                }
            }
            
            @keyframes go901347462 {
                from {
                    transform: scale(0.6);
                    opacity: 0.4;
                }
                to {
                    transform: scale(1);
                    opacity: 1;
                }
            }
            
            .go4109123758 {
                z-index: 9999;
            }
            
            .go4109123758>* {
                pointer-events: auto;
            }
        </style>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />


        <!-- Notification Popup 
    <div class="notification-popup" id="notification">
        <div class="d-flex align-items-center">
            <i class="fas fa-bell me-2" style="color: var(--main-color);"></i>
            <span>Welcome! Check out our new games</span>
        </div>
    </div>

  
    <div class="popup-overlay" id="mainPopup">
        <div class="popup-content">
            <span class="close-popup" id="closePopup">&times;</span>
            <h2 class="popup-title">Special Offer!</h2>
            <p class="popup-text">Get 50% bonus on your first deposit. Limited time offer for new members only.</p>
            <div class="text-center mt-4">
                <img src="https://cdn-icons-png.flaticon.com/512/869/869869.png" alt="Bonus" style="width: 100px; height: auto; margin-bottom: 20px;">
            </div>
            <button class="popup-btn" id="claimOffer">Claim Bonus Now</button>
        </div>
    </div> -->
        <!-- Welcome Blast Animation (only for logged-in users) -->
        <div id="welcomeBlastContainer" class="welcome-blast-container" style="display: none;">
            <div class="blast-wave"></div>
            <div class="blast-wave"></div>
            <div class="blast-wave"></div>
            <div class="blast-particles" id="blastParticles"></div>
            <div class="welcome-text" id="welcomeText">Welcome Back!</div>
        </div>

        <div id="root">
            <div class="mainApp">
                <div style="position: fixed; z-index: 9999; inset: 16px; pointer-events: none;"></div>
                <nav class="container-fluid mainnavbar">
                    <div class="container">
                        <div class="row">
                            <div class="col-12">
                                <div class="minlogobtn">
                                    <div class="logo">
                                        <a class="anker" href="/"><img style="width:85%;" class="h-100" src="././index_files/h5setting_202401100608011fs2.png" alt=""></a>
                                    </div>
                                    <div class="buttonGroup"><a class="btn btn1" href="/login">Log in</a><a class="btn btn2" href="#">Download App</a></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </nav>
                <div class="Homecss">
                    <div class="container-fluid herosection">
                        <div class="container">
                            <div class="row">
                                <div class="col-12 px-0 pt-1">
                                    <div class="swiper swiper-initialized swiper-horizontal mySwiper swiper-backface-hidden">
                                        <div class="swiper-wrapper" style="transition-duration: 300ms; transform: translate3d(-1338px, 0px, 0px);">
                                            <div class="swiper-slide swiper-slide-next" style="width: 416px; margin-right: 30px;" data-swiper-slide-index="2"><img src="/images/Banner3-Byk0IEg4.png" alt="baner" class="w-100 h-100"></div>
                                            <div class="swiper-slide" data-swiper-slide-index="3" style="width: 416px; margin-right: 30px;"><img src="/images/Banner4-BY7_CTAP.png" alt="baner" class="w-100 h-100"></div>
                                            <div class="swiper-slide swiper-slide-prev" style="width: 416px; margin-right: 30px;" data-swiper-slide-index="0"><img src="/images/Banner1-DNcC_heK.png" alt="baner" class="w-100 h-100"></div>
                                            <div class="swiper-slide swiper-slide-active" style="width: 416px; margin-right: 30px;" data-swiper-slide-index="1"><img src="/images/Banner2-DzfyupcJ.png" alt="baner" class="w-100 h-100"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid bottombar my-2">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <div class="speekerimage"><svg id="icon-noticeBarSpeaker" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="none"><g clip-path="url(#clip0_9095_3163)"><path d="M15.9993 4V28C11.3327 28 7.86502 21.8927 7.86502 21.8927H3.99935C3.26297 21.8927 2.66602 21.2958 2.66602 20.5594V11.3405C2.66602 10.6041 3.26297 10.0072 3.99935 10.0072H7.86502C7.86502 10.0072 11.3327 4 15.9993 4Z" fill="url(#paint0_linear_9095_3163)"></path><path d="M21.334 10C21.7495 10.371 22.1261 10.7865 22.4567 11.2392C23.4265 12.5669 24.0007 14.2149 24.0007 16C24.0007 17.7697 23.4363 19.4045 22.4819 20.7262C22.1452 21.1923 21.7601 21.6195 21.334 22" stroke="url(#paint1_linear_9095_3163)" stroke-width="1.8" stroke-linecap="round" stroke-linejoin="round"></path><path d="M22.8242 27.4568C26.7227 25.1298 29.3336 20.8696 29.3336 15.9996C29.3336 11.2053 26.8031 7.00197 23.005 4.65234" stroke="url(#paint2_linear_9095_3163)" stroke-width="1.8" stroke-linecap="round"></path></g><defs><linearGradient id="paint0_linear_9095_3163" x1="9.33268" y1="4" x2="9.33268" y2="28" gradientUnits="userSpaceOnUse"><stop stop-color="var(--blackGoldN,var(--main-color))"></stop><stop offset="0.74876" stop-color="var(--main-color)"></stop></linearGradient><linearGradient id="paint1_linear_9095_3163" x1="22.6673" y1="10" x2="22.6673" y2="22" gradientUnits="userSpaceOnUse"><stop stop-color="var(--blackGoldN,var(--main-color))"></stop><stop offset="0.74876" stop-color="var(--main-color)"></stop></linearGradient><linearGradient id="paint2_linear_9095_3163" x1="26.0789" y1="4.65234" x2="26.0789" y2="27.4568" gradientUnits="userSpaceOnUse"><stop stop-color="var(--blackGoldN,var(--main-color))"></stop><stop offset="0.74876" stop-color="var(--main-color)"></stop></linearGradient><clipPath id="clip0_9095_3163"><rect width="32" height="32" fill="white"></rect></clipPath></defs></svg></div>
                                </div>
                                <div class="col">
                                    <div class="middlecontent">
                                        <p></p>
                                    </div>
                                </div>
                                <div class="col-auto"><button class="btn mainbtn rounded-pill py-1 px-3 d-flex align-items-center gap-1 shadow-none"><span><img class="w-100 h-100" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADiSURBVHgB3ZThDYIwEIVPwgCM0A10A+sGjsAIjOIG4AZuICOwATiBIzyv0sTSYFta+KFf8nIhB+9dG1qiXwFAwSppC7R5jRFJa6GNK1aPD5X5Th5gIrkcWaoKqy1mPimCAtR0XGrWmRLIv5gLLnean9BH5+wqc2tPlyJ8AQ3iaX3mEmmUW07fz3lm1vOe4hhYJw45sC5mY2etABTHoKt4mzJrB0wwA+wtelA6k3OQuZpbBDSUztXZRdopbsmH/tWizgF814QRUmIZTzUYLQHhl14bPLljNTdWZ5gOGK8VSX/PC6g7TiHTGMGPAAAAAElFTkSuQmCC" alt="fair"></span> Detail</button></div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid cardSection">
                        <div class="container">
                            <div class="row g-3">
                                <div class="col-6 px-1">
                                    <a class="card card1 border-0    h-100" href="/">
                                        <div class="card-body p-0">
                                            <div class="rightimage"><img class="w-100 h-100  " src="/images/trophy-C0rXp2y4.png" alt="trop"></div>
                                            <div class="cardtext   ">Popular</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-6 px-1">
                                    <a href="#popler" class="card card2 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="rightimage"><img class="w-100 h-100  " src="/images/lottery-DQ0vQZqG.png" alt="lot"></div>
                                            <div class="cardtext   ">Lottery</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4  px-1" style="    margin-top: 14px;">
                                    <a href="#Casino" class="card card3 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="rightimage"><img class="w-100 h-100  " src="/images/casino-BS8rswiM.png" alt="casin"></div>
                                            <div class="cardtext   ">Casino</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4 px-1" style="    margin-top: 14px;">
                                    <a href="#Slots" class="card card4 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="smallimage"><img class="w-100 h-100  " src="/images/slots-DrA-QBhG.png" alt="casin"></div>
                                            <div class="cardtext  ">Slots</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4 px-1" style="    margin-top: 14px;">
                                    <a href="#Sports" class="card card5 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="smallimage"><img class="w-100 h-100  " src="/images/sport-BvUXhixI.png" alt="casin"></div>
                                            <div class="cardtext   ">Sports</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4 px-1" style="    margin-top: 14px;">
                                    <a href="#Rummy" class="card card6 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="smallimage"><img class="w-100 h-100  " src="/images/rummey-CIslnG31.png" alt="casin"></div>
                                            <div class="cardtext   ">Rummy</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4 px-1" style="    margin-top: 14px;">
                                    <a href="#Fishing" class="card card7 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="smallimage"><img class="w-100 h-100  " src="/images/fish-M51pHW8e.png" alt="casin"></div>
                                            <div class="cardtext   ">Fishing</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-4 px-1" style="    margin-top: 14px;">
                                    <a href="#spribe" class="card card8 border-0    h-100">
                                        <div class="card-body p-0">
                                            <div class="smallimage"><img class="w-100 h-100 " src="/images/avitor-C5JlOTlk.png" alt="casin"></div>
                                            <div class="cardtext   ">Original</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid lotterySection my-3" id="popler">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">Lottery</div><button>All <span class="text-theme1">4</span> <span><i class="fa-solid fa-angle-right"></i></span></button></div>
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-12 px-1">
                                    <a class="card card1 text-decoration-none" href="/Win">
                                        <div class="card-body pade">
                                            <div class="heading">Win Go </div>
                                            <div class="gameslott">
                                                <div class="card-image"><img class="w-100 h-100" src="/images/lotterycategory-xDA-xlGB.png" alt="imges"></div><button class="manincardbtn">GO<span><i class="fa-solid fa-angle-right text-white"></i></span></button></div>
                                        </div>
                                        <div class="di">
                                            <div class="textw">Member:111000</div>
                                            <div class="textw">Winning Amount: 2020</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-12 px-1">
                                    <a class="card card2 text-decoration-none" href="/k3">
                                        <div class="card-body pade">
                                            <div class="heading">K3</div>
                                            <div class="gameslott">
                                                <div class="card-image"><img class="w-100 h-100" src="/images/passa-BlXJHPeo.png" alt="imges"></div><button class="manincardbtn">GO<span><i class="fa-solid fa-angle-right text-white"></i></span></button></div>
                                        </div>
                                        <div class="di">
                                            <div class="textw">Member:111000</div>
                                            <div class="textw">Winning Amount: 2020</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-12 px-1">
                                    <a class="card card2 text-decoration-none" href="/5d">
                                        <div class="card-body pade">
                                            <div class="heading">5D</div>
                                            <div class="gameslott">
                                                <div class="card-image"><img class="w-100 h-100" src="/images/lotterycategory_20240123160137lok5.png" alt="imges"></div><button class="manincardbtn">GO<span><i class="fa-solid fa-angle-right text-white"></i></span></button></div>
                                        </div>
                                        <div class="di">
                                            <div class="textw">Member:111000</div>
                                            <div class="textw">Winning Amount: 2020</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-12 px-1">
                                    <a class="card card4 text-decoration-none" href="/trx">
                                        <div class="card-body pade">
                                            <div class="heading">Trx Win Go</div>
                                            <div class="gameslott">
                                                <div class="card-image"><img class="w-100 h-100" src="/images/card4coin-CFRIva-c.png" alt="imges"></div><button class="manincardbtn">GO<span><i class="fa-solid fa-angle-right text-white"></i></span></button></div>
                                        </div>
                                        <div class="di">
                                            <div class="textw">Member:111000</div>
                                            <div class="textw">Winning Amount: 2020</div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-12 px-1">
                                    <a class="card card4 text-decoration-none" href="/aviator">
                                        <div class="card-body pade">
                                            <div class="heading">Aviator</div>
                                            <div class="gameslott">
                                                <div class="card-image"><img class="w-100 h-100" src="/images/aviatoricon.png" alt="imges"></div><button class="manincardbtn">GO<span><i class="fa-solid fa-angle-right text-white"></i></span></button></div>
                                        </div>
                                        <div class="di">
                                            <div class="textw">Member:111000</div>
                                            <div class="textw">Winning Amount: 2020</div>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="Jdb">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">SPRIBE</div><a class="button" href="/spribeAllpage">All <span class="text-theme1">9</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-aviator_en_US&amp;type=logo-square" alt="Game Aviator" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-dice_en_US&amp;type=logo-square" alt="Game Dice" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-goal_en_US&amp;type=logo-square" alt="Game Goal" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-hilo_en_US&amp;type=logo-square" alt="Game Hilo" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-hotline_en_US&amp;type=logo-square" alt="Game Hotline" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=SPB-mines_en_US&amp;type=logo-square" alt="Game Mines" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="EVOLUTION" style="display: none;">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">EVOLUTION</div><a class="button" href="/SlotsAllpage">All <span class="text-theme1">88</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-dhp_en_US&amp;type=logo-square" alt="Game 2 Hand Casino Hold'em" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-autolightningroulette_en_US&amp;type=logo-square" alt="Game Auto Lightning Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-autoroulettelapartage_en_US&amp;type=logo-square" alt="Game Autoroulette La Partage" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-americanroulette_en_US&amp;type=logo-square" alt="Game American Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-bacbo_en_US&amp;type=logo-square" alt="Game Bac Bo" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EVO-baccarat_en_US&amp;type=logo-square" alt="Game Baccarat" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="vivo">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">VIVO</div><a class="button" href="/jdbAllpage">All <span class="text-theme1">23</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-lobby_en_US&amp;type=logo-square" alt="Game Vivo Gaming Lobby" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-americanautoroulette_en_US&amp;type=logo-square" alt="Game American Auto Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-bulgariaroulette_en_US&amp;type=logo-square" alt="Game Bulgaria Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-frenchroulette_en_US&amp;type=logo-square" alt="Game French Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-galaxybaccarat1_en_US&amp;type=logo-square" alt="Game Galaxy Baccarat 1" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=VGL-galaxybaccarat3_en_US&amp;type=logo-square" alt="Game Galaxy Baccarat 3" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="7mojos">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">7MOJOS</div><a class="button" href="/jdbSlotAllPges">All <span class="text-theme1">72</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=7MC-wildjokerfrenzy_en_US&amp;type=logo-square" alt="Game Wild Joker Frenzy" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src=" https://client.qtlauncher.com/images/?id=7MC-wildjokerfrenzy_en_US&amp;type=logo-square" alt="Game Andar Bahar" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src=" https://client.qtlauncher.com/images/?id=7MC-andarbahar_en_US&amp;type=logo-square" alt="Game Teen Patti" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src=" https://client.qtlauncher.com/images/?id=7MC-teenpatti_en_US&amp;type=logo-square" alt="Game Teen Patti Face Off" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src=" https://client.qtlauncher.com/images/?id=7MC-teenpattifaceoff_en_US&amp;type=logo-square" alt="Game 3-Card Poker" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src=" https://client.qtlauncher.com/images/?id=7MC-3cardpoker_en_US&amp;type=logo-square" alt="Game Auto Roulette Royal" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="EZUGI">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">EZUGI</div><a class="button" href="/CasinoAllpage">All <span class="text-theme1">170</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-skylineroulette_en_US&amp;type=logo-square" alt="Game Skyline Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-viproulette_en_US&amp;type=logo-square" alt="Game VIP Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-speedautoroulette_en_US&amp;type=logo-square" alt="Game Speed Auto Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-cricketautoroulette_en_US&amp;type=logo-square" alt="Game Cricket Auto Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-casinomarinaroulette1_en_US&amp;type=logo-square" alt="Game Casino Marina Roulette 1" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=EZU-oracle360roulette_en_US&amp;type=logo-square" alt="Game Oracle 360 Roulette" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="AVATAR">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">AVATAR</div><a class="button" href="/jdbCasinoAllPage">All <span class="text-theme1">46</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-juicypop_en_US&amp;type=logo-square" alt="Game JuicyPop" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-sweetgummy_en_US&amp;type=logo-square" alt="Game Sweet Gummy" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-beastlyburglars_en_US&amp;type=logo-square" alt="Game Beastly Burglars" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-depthsoffortune_en_US&amp;type=logo-square" alt="Game Depths of Fortune" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-arcanapop_en_US&amp;type=logo-square" alt="Game ArcanaPop" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=AUX-floridaman_en_US&amp;type=logo-square" alt="Game Floridaman" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="Fishing">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">B Gaming</div><a class="button" href="/FishingAllpage">All <span class="text-theme1">98</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-alohakingelvis_en_US&amp;type=logo-square" alt="Game Aloha King Elvis" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-americanroulette_en_US&amp;type=logo-square" alt="Game American Roulette" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-avalonthelostkingdom_en_US&amp;type=logo-square" alt="Game Avalon: The Lost Kingdom" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-aztecmagic_en_US&amp;type=logo-square" alt="Game Aztec Magic" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-aztecmagicbonanza_en_US&amp;type=logo-square" alt="Game Aztec Magic Bonanza" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BGM-aztecmagicdeluxe_en_US&amp;type=logo-square" alt="Game Aztec Magic Deluxe" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="Fishing">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">BB Game</div><a class="button" href="/">All <span class="text-theme1">1</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BBG-yinandyang_en_US&amp;type=logo-square" alt="Game Yin &amp; Yang" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="Live" style="display: none;">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">Better Live</div><a class="button" href="/BetterLiveAll">All <span class="text-theme1">41</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-topcard_en_US&amp;type=logo-square" alt="Game Top Card" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-roulettelite_en_US&amp;type=logo-square" alt="Game Roulette Lite" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-vipblackjack_en_US&amp;type=logo-square" alt="Game VIP Blackjack" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-virtualdragontiger_en_US&amp;type=logo-square" alt="Game Virtual Dragon Tiger" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-thekickoff_en_US&amp;type=logo-square" alt="Game The Kickoff" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTL-gravityroulette_en_US&amp;type=logo-square" alt="Game Gravity Roulette" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3" id="Fishing">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">Big Time Game</div><a class="button" href="/btgallpage">All <span class="text-theme1">71</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-kingkongcashevenbiggerbananas2_en_US&amp;type=logo-square" alt="Game King Kong Cash Even Bigger Bananas 2" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTG-boo_en_US&amp;type=logo-square" alt="Game Boo" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTG-bookofgods_en_US&amp;type=logo-square" alt="Game Book of Gods" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTG-millionairemysterybox_en_US&amp;type=logo-square" alt="Game Millionaire Mystery Box" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTG-holydivermegaways_en_US&amp;type=logo-square" alt="Game Holy Diver Megaways" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BTG-fizzypennyslot_en_US&amp;type=logo-square" alt="Game Fizzy Pennyslot" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">Blueprint Game</div><a class="button" href="/bpgallpage">All <span class="text-theme1">266</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-eaglestorm_en_US&amp;type=logo-square" alt="Game Eagle Storm" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-almightybearmegaways_en_US&amp;type=logo-square" alt="Game Almighty Bear Megaways" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-biggercatchbassfishing_en_US&amp;type=logo-square" alt="Game Bigger Catch Bass Fishing" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-majesticfurymegaways_en_US&amp;type=logo-square" alt="Game Majestic Fury Megaways" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-stickermaniawildrumble_en_US&amp;type=logo-square" alt="Game Stickermania Wild Rumble" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=BPG-trailblazer_en_US&amp;type=logo-square" alt="Game Trailblazer" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid OrignalSection my-3">
                        <div class="container">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="topcontent">
                                        <div class="leftheading">OneX2 Game</div><a class="button" href="/onex2allpage">All <span class="text-theme1">87</span> <span><i class="fa-solid fa-angle-right"></i></span></a></div>
                                </div>
                            </div>
                            <div id="games-container" class="row g-3">
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-420blazeit_en_US&amp;type=logo-square" alt="Game 420 Blaze It" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-spinofootball_en_US&amp;type=logo-square" alt="Game Spino Football" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-spinogreyhounds_en_US&amp;type=logo-square" alt="Game Spino Greyhounds" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-spinohorses_en_US&amp;type=logo-square" alt="Game Spino Horses" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-crashoutfirework_en_US&amp;type=logo-square" alt="Game Crashout - Firework" class="w-100 h-100"></a>
                                </div>
                                <div class="col-4 px-1">
                                    <a href="#" class="jili_games_card"><img src="https://client.qtlauncher.com/images/?id=1x2-dogsquad_en_US&amp;type=logo-square" alt="Game Dog Squad" class="w-100 h-100"></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container-fluid notifictionalllist ">
                        <div class="container">
                            <div class="row g-2">
                                <div class="col-12"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <footer class="container-fluid footermain mainApp px-1">
                    <div class="row w-100">
                        <div class="col-12 px-1 w-100">
                            <div class="footerlist">
                                <a class="tabbar__container-item text-decoration-none active" href="/">
                                    <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2046C36.9411%2046%2045%2037.9411%2045%2028C45%2018.0589%2036.9411%2010%2027%2010C17.0589%2010%209%2018.0589%209%2028C9%2037.9411%2017.0589%2046%2027%2046Z'%20fill='black'/%3e%3cpath%20d='M23.6599%205.27799L5.76039%2017.3574V42.8644H15.8404V32.4244C15.8404%2029.6409%2018.0969%2027.3844%2020.8804%2027.3844H27.2404C30.0239%2027.3844%2032.2804%2029.6409%2032.2804%2032.4244V36.8044H28.9204V32.4244C28.9204%2031.4965%2028.1682%2030.7444%2027.2404%2030.7444H20.8804C19.9526%2030.7444%2019.2004%2031.4965%2019.2004%2032.4244V43.5844C19.2004%2045.0424%2018.0184%2046.2244%2016.5604%2046.2244H5.04039C3.58236%2046.2244%202.40039%2045.0424%202.40039%2043.5844V16.9747C2.40039%2016.0973%202.83631%2015.2772%203.56361%2014.7863L22.4377%202.04925C23.1552%201.56501%2024.0926%201.55594%2024.8194%202.02622L44.5146%2014.7702C45.2664%2015.2567%2045.7204%2016.0911%2045.7204%2016.9866V43.5844C45.7204%2045.0424%2044.5384%2046.2244%2043.0804%2046.2244H30.6004V42.8644H42.3604V17.3783L23.6599%205.27799Z'%20fill='black'/%3e%3cpath%20d='M32.4%2044.5443C32.4%2045.4722%2031.6478%2046.2243%2030.72%2046.2243C29.7922%2046.2243%2029.04%2045.4722%2029.04%2044.5443C29.04%2043.6165%2029.7922%2042.8643%2030.72%2042.8643C31.6478%2042.8643%2032.4%2043.6165%2032.4%2044.5443Z'%20fill='black'/%3e%3cpath%20d='M32.2799%2036.7445C32.2799%2037.6724%2031.5277%2038.4245%2030.5999%2038.4245C29.6721%2038.4245%2028.9199%2037.6724%2028.9199%2036.7445C28.9199%2035.8167%2029.6721%2035.0645%2030.5999%2035.0645C31.5277%2035.0645%2032.2799%2035.8167%2032.2799%2036.7445Z'%20fill='black'/%3e%3c/svg%3e"
                                            alt=""></div><span>Home</span></a>
                                <a class="tabbar__container-item text-decoration-none" href="/checkIn">
                                    <div class="icon"><img class="w-100 h-100" src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M27%2042C36.9411%2042%2045%2033.9411%2045%2024C45%2014.0589%2036.9411%206%2027%206C17.0589%206%209%2014.0589%209%2024C9%2033.9411%2017.0589%2042%2027%2042Z'%20fill='black'/%3e%3cpath%20d='M17.4489%2016.6808C17.4491%2016.6697%2017.4492%2016.6586%2017.4492%2016.6475C17.4492%2015.681%2016.6833%2014.8975%2015.7168%2014.8975C14.7529%2014.8975%2013.9263%2015.6767%2013.9219%2016.6396C13.9222%2016.6432%2013.9215%2016.6469%2013.9219%2016.6505C13.9221%2016.7723%2013.9355%2016.8911%2013.9606%2017.0059C14.5925%2021.9877%2018.8462%2025.8397%2023.9996%2025.8397C29.1037%2025.8397%2033.3252%2022.0611%2034.0195%2017.1487C34.0666%2016.9905%2034.0918%2016.8229%2034.0918%2016.6494C34.0918%2015.6829%2033.3083%2014.8994%2032.3418%2014.8994C31.3753%2014.8994%2030.5469%2015.6829%2030.5469%2016.6494C30.5469%2016.6662%2030.5471%2016.6829%2030.5476%2016.6996C30.0741%2019.8911%2027.3228%2022.3397%2023.9996%2022.3397C20.67%2022.3397%2017.9144%2019.8815%2017.4489%2016.6808Z'%20fill='black'/%3e%3cpath%20d='M10.5119%205.2H37.487C40.0599%205.2%2042.1753%207.22874%2042.2828%209.79945L42.793%2022H42.7995C42.7995%2022.8836%2043.5158%2023.6005%2044.3995%2023.6005C45.2832%2023.6005%2045.9995%2022.8841%2045.9995%2022.0005C45.9995%2021.9341%2045.9955%2021.8686%2045.9876%2021.8044L45.48%209.66575C45.3008%205.38123%2041.7752%202%2037.487%202H10.5119C6.22361%202%202.69803%205.38123%202.51886%209.66575L1.34795%2037.6657C1.15787%2042.2112%204.79154%2046%209.34096%2046H38.6579C43.2073%2046%2046.841%2042.2112%2046.6509%2037.6657L46.3303%2030C46.3069%2029.1368%2045.5993%2028.4442%2044.7304%2028.4442C43.8615%2028.4442%2043.1544%2029.1368%2043.131%2030H43.1275L43.1307%2030.0763C43.1312%2030.1004%2043.1322%2030.1243%2043.1337%2030.148L43.4537%2037.7994C43.5677%2040.5267%2041.3875%2042.8%2038.6579%2042.8H9.34096C6.61131%2042.8%204.43111%2040.5267%204.54516%2037.7994L5.71607%209.79945C5.82357%207.22874%207.93892%205.2%2010.5119%205.2Z'%20fill='black'/%3e%3c/svg%3e"
                                            alt=""></div><span>Activity</span></a>
                                <a class="tabbar__container-item text-decoration-none " href="/promotion">
                                    <div class="icon diamondimage">
                                        <div class="image"><svg id="icon-promotion" viewBox="0 0 57 49" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.93876 1.50122C9.69785 0.55236 10.8471 0 12.0622 0H44.2172C45.4324 0 46.5816 0.552359 47.3407 1.50122L55.0792 11.1744C55.5056 11.7073 55.828 12.2943 56.0469 12.9092H0.232598C0.451468 12.2943 0.773925 11.7073 1.20023 11.1744L8.93876 1.50122ZM0 16.091H56.2795C56.0896 17.0496 55.664 17.9709 55.0034 18.7637L31.2126 47.3125C29.6134 49.2316 26.666 49.2316 25.0669 47.3125L1.27612 18.7637C0.615521 17.9709 0.189841 17.0496 0 16.091ZM20.5563 22.0266L27.7513 32.1286C27.9512 32.4093 28.3685 32.4083 28.5671 32.1267L35.6853 22.0338C36.1425 21.3856 36.8863 21 37.6795 21C39.0272 21 40.1198 22.0925 40.1198 23.4403V23.6393H39.8972C39.5712 23.6393 39.1148 23.8877 38.5931 24.5708C38.0874 25.2331 32.1271 33.2938 28.9417 37.6047C28.7578 37.8535 28.467 38 28.1577 38C27.8515 38 27.5632 37.8562 27.379 37.6117L17.3204 24.2603C17.3204 24.2603 16.9258 23.6393 16.2608 23.6393H16.1198V23.445C16.1198 22.0947 17.2144 21 18.5648 21C19.3556 21 20.0975 21.3825 20.5563 22.0266Z" fill="white"></path></svg></div>
                                    </div><span class="permotiontext">Promotion</span></a>
                                <a class="tabbar__container-item text-decoration-none" href="/wallet">
                                    <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M3%2023C3%2017.016%206.526%2012.836%2012.0085%2012.132C12.5675%2012.044%2013.148%2012%2013.75%2012H35.25C35.809%2012%2036.3465%2012.022%2036.8625%2012.11C42.4095%2012.77%2046%2016.972%2046%2023V34C46%2040.6%2041.7%2045%2035.25%2045H13.75C7.3%2045%203%2040.6%203%2034V31.822'%20stroke='black'/%3e%3cpath%20d='M46%2023.7241H39.548C37.1822%2023.7241%2035.2466%2025.5862%2035.2466%2027.8621C35.2466%2030.1379%2037.1822%2032%2039.548%2032H46M37%2012C36.4838%2011.9172%2035.8058%2012%2035.2466%2012H14C13.3978%2012%2012.5592%2011.9172%2012%2012C12%2012%2012.7312%2011.3517%2013.2474%2010.8551L20.2371%204.11027C21.6566%202.75836%2023.5733%202%2025.5708%202C27.5682%202%2029.4849%202.75836%2030.9044%204.11027L34.6681%207.77235C36.0445%209.0758%2039.548%2012%2037%2012Z'%20stroke='black'/%3e%3c/svg%3e"
                                            alt="" class="w-100 h-100"></div><span>Wallet</span></a>
                                <a class="tabbar__container-item text-decoration-none" href="/mian">
                                    <div class="icon"><img src="data:image/svg+xml,%3csvg%20width='48'%20height='48'%20viewBox='0%200%2048%2048'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20opacity='0.3'%20d='M28%2042C37.9411%2042%2046%2033.9411%2046%2024C46%2014.0589%2037.9411%206%2028%206C18.0589%206%2010%2014.0589%2010%2024C10%2033.9411%2018.0589%2042%2028%2042Z'%20fill='black'/%3e%3cpath%20d='M24.08%205.27992C13.7412%205.27992%205.36%2013.6612%205.36%2023.9999C5.36%2034.3387%2013.7412%2042.7199%2024.08%2042.7199C34.4188%2042.7199%2042.8%2034.3387%2042.8%2023.9999V15.2399H46.16V23.9999C46.16%2036.1944%2036.2744%2046.0799%2024.08%2046.0799C11.8856%2046.0799%202%2036.1944%202%2023.9999C2%2011.8055%2011.8856%201.91992%2024.08%201.91992H44.36V5.27992H24.08Z'%20fill='black'/%3e%3cpath%20d='M46.1598%203.59992C46.1598%204.52776%2045.4076%205.27992%2044.4798%205.27992C43.552%205.27992%2042.7998%204.52776%2042.7998%203.59992C42.7998%202.67208%2043.552%201.91992%2044.4798%201.91992C45.4076%201.91992%2046.1598%202.67208%2046.1598%203.59992Z'%20fill='black'/%3e%3cpath%20d='M46.1598%2015.1195C46.1598%2016.0474%2045.4076%2016.7995%2044.4798%2016.7995C43.552%2016.7995%2042.7998%2016.0474%2042.7998%2015.1195C42.7998%2014.1917%2043.552%2013.4395%2044.4798%2013.4395C45.4076%2013.4395%2046.1598%2014.1917%2046.1598%2015.1195Z'%20fill='black'/%3e%3cpath%20d='M15.8064%2029.5825C16.501%2028.9674%2017.5627%2029.0317%2018.1779%2029.7263C19.3275%2031.0242%2020.9265%2032.5202%2023.6403%2032.5202C26.5117%2032.5202%2028.4971%2031.0925%2029.4448%2029.9868C30.0486%2029.2824%2031.1092%2029.2008%2031.8136%2029.8046C32.5181%2030.4085%2032.5997%2031.469%2031.9959%2032.1735C30.5435%2033.8679%2027.6809%2035.8802%2023.6403%2035.8802C19.4421%2035.8802%2016.9931%2033.4562%2015.6627%2031.9541C15.0475%2031.2595%2015.1118%2030.1977%2015.8064%2029.5825Z'%20fill='black'/%3e%3c/svg%3e"
                                            alt="" class="w-100 h-100"></div><span>Account</span></a>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>
        <!-- Swiper JS -->
        <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
        <!-- Bootstrap JS Bundle -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"></script>

        <script>
            // Initialize Swiper slider
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Swiper
                var swiper = new Swiper(".mySwiper", {
                    slidesPerView: 1,
                    spaceBetween: 10,
                    loop: true,
                    autoplay: {
                        delay: 3000,
                        disableOnInteraction: false,
                    },
                    pagination: {
                        el: ".swiper-pagination",
                        clickable: true,
                    },
                    breakpoints: {
                        768: {
                            slidesPerView: 1.2,
                            spaceBetween: 20,
                        },
                        992: {
                            slidesPerView: 1.5,
                            spaceBetween: 30,
                        }
                    }
                });

                // Show main popup after 2 seconds (only if popup exists)
                const mainPopup = document.getElementById('mainPopup');
                if (mainPopup) {
                    setTimeout(function() {
                        mainPopup.classList.add('active');
                    }, 2000);
                }

                // Show notification after 4 seconds (only if notification exists)
                const notification = document.getElementById('notification');
                if (notification) {
                    setTimeout(function() {
                        notification.classList.add('show');

                        // Hide notification after 5 seconds
                        setTimeout(function() {
                            notification.classList.remove('show');
                        }, 5000);
                    }, 4000);
                }

                // Close popup functionality (only if elements exist)
                const closePopup = document.getElementById('closePopup');
                if (closePopup && mainPopup) {
                    closePopup.addEventListener('click', function() {
                        mainPopup.classList.remove('active');
                    });
                }

                // Claim offer button (only if elements exist)
                const claimOffer = document.getElementById('claimOffer');
                if (claimOffer && mainPopup) {
                    claimOffer.addEventListener('click', function() {
                        alert('Bonus claimed successfully!');
                        mainPopup.classList.remove('active');
                    });
                }

                // Close popup when clicking outside
                document.getElementById('mainPopup').addEventListener('click', function(e) {
                    if (e.target === this) {
                        this.classList.remove('active');
                    }
                });

                // Game card interactions
                const gameCards = document.querySelectorAll('.jili_games_card');
                gameCards.forEach(card => {
                    card.addEventListener('click', function(e) {
                        e.preventDefault();
                        alert('Game loading...');
                    });
                });

                // Category card interactions
                const categoryCards = document.querySelectorAll('.card');
                categoryCards.forEach(card => {
                    card.addEventListener('click', function(e) {
                        if (this.getAttribute('href') === '#') {
                            e.preventDefault();
                            alert('Category selected!');
                        }
                    });
                });

                // Footer navigation
                const navItems = document.querySelectorAll('.tabbar__container-item');
                navItems.forEach(item => {
                    item.addEventListener('click', function() {
                        navItems.forEach(i => i.classList.remove('active'));
                        this.classList.add('active');
                    });
                });

                // Bottom bar animation
                const bottomBar = document.querySelector('.bottombar');
                bottomBar.addEventListener('click', function() {
                    alert('Viewing details...');
                });

                // Auto-scrolling for middle content
                const middleContent = document.querySelector('.middlecontent p');

                // Welcome Blast Animation for Logged-in Users
                checkUserLoginAndShowBlast();
            });

            // Function to check if user is logged in and show blast animation
            function checkUserLoginAndShowBlast() {
                // Check if animation was already shown in this session
                if (sessionStorage.getItem('welcomeBlastShown')) {
                    return;
                }

                // Check if user is logged in by making API call
                fetch('/api/webapi/GetUserInfo')
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === true && data.data) {
                            // User is logged in, show welcome blast animation
                            showWelcomeBlast(data.data.name_user || 'Member');
                            // Mark animation as shown for this session
                            sessionStorage.setItem('welcomeBlastShown', 'true');
                        }
                    })
                    .catch(error => {
                        console.log('User not logged in or error checking login status');
                    });
            }

            // Function to show the welcome blast animation
            function showWelcomeBlast(userName) {
                const blastContainer = document.getElementById('welcomeBlastContainer');
                const welcomeText = document.getElementById('welcomeText');
                const particlesContainer = document.getElementById('blastParticles');

                // Update welcome text with user name
                welcomeText.textContent = `Welcome Back, ${userName}!`;

                // Show the blast container
                blastContainer.style.display = 'block';

                // Create particles for the blast effect
                createBlastParticles(particlesContainer);

                // Create sparkles
                createSparkles(blastContainer);

                // Hide the animation after 4 seconds
                setTimeout(() => {
                    blastContainer.classList.add('fade-out-animation');
                    setTimeout(() => {
                        blastContainer.style.display = 'none';
                        blastContainer.classList.remove('fade-out-animation');
                    }, 1000);
                }, 4000);
            }

            // Function to create blast particles
            function createBlastParticles(container) {
                const particleCount = 50;

                for (let i = 0; i < particleCount; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'particle';

                    // Random position around the center
                    const angle = (Math.PI * 2 * i) / particleCount;
                    const distance = Math.random() * 200 + 50;
                    const x = Math.cos(angle) * distance;
                    const y = Math.sin(angle) * distance;

                    particle.style.left = x + 'px';
                    particle.style.top = y + 'px';

                    // Random colors
                    const colors = ['#FFD700', '#FF6B35', '#00BFFF', '#FF1493', '#32CD32', '#FF4500'];
                    particle.style.background = colors[Math.floor(Math.random() * colors.length)];

                    // Random animation delay
                    particle.style.animationDelay = Math.random() * 0.5 + 's';

                    container.appendChild(particle);
                }

                // Clean up particles after animation
                setTimeout(() => {
                    container.innerHTML = '';
                }, 3000);
            }

            // Function to create sparkle effects
            function createSparkles(container) {
                const sparkleCount = 30;

                for (let i = 0; i < sparkleCount; i++) {
                    const sparkle = document.createElement('div');
                    sparkle.className = 'sparkle';

                    // Random position
                    sparkle.style.left = Math.random() * 100 + '%';
                    sparkle.style.top = Math.random() * 100 + '%';

                    // Random animation delay
                    sparkle.style.animationDelay = Math.random() * 2 + 's';

                    container.appendChild(sparkle);
                }

                // Clean up sparkles after animation
                setTimeout(() => {
                    const sparkles = container.querySelectorAll('.sparkle');
                    sparkles.forEach(sparkle => sparkle.remove());
                }, 4000);
            }
        </script>

    </body>

</html>