<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Extra Invite Bonus - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Daily Extra Invite Bonus</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-gradient-primary text-white border-0 rounded-3">
                            <div class="card-body text-center">
                                <h6 class="card-title mb-2">Total Invite Bonus</h6>
                                <h2 class="mb-0" id="total-bonus">₹0.00</h2>
                                <small id="bonus-count">0 bonuses available</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bonus Statistics -->
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body text-center py-2">
                                <i class="fa-solid fa-check-circle text-success mb-2" style="font-size: 24px;"></i>
                                <h6 class="card-title mb-1">Claimed</h6>
                                <h5 class="mb-0 text-success" id="claimed-count">0</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body text-center py-2">
                                <i class="fa-solid fa-clock text-warning mb-2" style="font-size: 24px;"></i>
                                <h6 class="card-title mb-1">Pending</h6>
                                <h5 class="mb-0 text-warning" id="pending-count">0</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Bonus History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="bonus-list" class="list-group list-group-flush">
                                    <div class="list-group-item text-center py-4">
                                        <i class="fa-solid fa-gift text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-2 mb-0 text-muted">No bonus data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Daily Extra Invite Bonus page loaded');
            loadInviteBonusData();
        });

        function loadInviteBonusData() {
            // Show loading state
            $('#total-bonus').text('Loading...');
            $('#bonus-list').html('<div class="list-group-item text-center py-4"><i class="fa-solid fa-spinner fa-spin text-muted" style="font-size: 48px;"></i><p class="mt-2 mb-0 text-muted">Loading bonus data...</p></div>');

            // Fetch invite bonus data
            $.ajax({
                type: "POST",
                url: "/api/webapi/inviteBonus",
                data: {},
                dataType: "json",
                success: function(response) {
                    console.log('Invite bonus response:', response);
                    if (response.status === true) {
                        updateBonusUI(response);
                    } else {
                        showError('Failed to load bonus data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading bonus data:', error);
                    showError('Error loading bonus data');
                }
            });
        }

        function updateBonusUI(data) {
            const bonusRecords = data.record || [];
            let totalBonus = 0;
            let claimedCount = 0;
            let pendingCount = 0;

            // Calculate totals
            bonusRecords.forEach(bonus => {
                totalBonus += parseFloat(bonus.amount || 0);
                if (bonus.status === 'success') {
                    claimedCount++;
                } else {
                    pendingCount++;
                }
            });

            // Update summary cards
            $('#total-bonus').text(`₹${totalBonus.toFixed(2)}`);
            $('#bonus-count').text(`${bonusRecords.length} bonuses available`);
            $('#claimed-count').text(claimedCount);
            $('#pending-count').text(pendingCount);

            // Render bonus list
            renderBonusList(bonusRecords);
        }

        function renderBonusList(bonusRecords) {
            const bonusList = $('#bonus-list');

            if (bonusRecords.length === 0) {
                bonusList.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-gift text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No invite bonus data available</p>
                    </div>
                `);
                return;
            }

            let bonusHTML = '';
            bonusRecords.forEach((bonus, index) => {
                        const statusBadge = bonus.status === 'success' ?
                            '<span class="badge bg-success">Claimed</span>' :
                            '<span class="badge bg-warning">Pending</span>';

                        const statusIcon = bonus.status === 'success' ?
                            '<i class="fa-solid fa-check-circle text-success"></i>' :
                            '<i class="fa-solid fa-clock text-warning"></i>';

                        bonusHTML += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            ${statusIcon}
                            <div class="ms-3">
                                <h6 class="mb-0">Invite Bonus #${bonus.id || (index + 1)}</h6>
                                <small class="text-muted">Amount: ₹${parseFloat(bonus.amount || 0).toFixed(2)}</small>
                                ${bonus.description ? `<br><small class="text-muted">${bonus.description}</small>` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            ${statusBadge}
                            ${bonus.created_at ? `<br><small class="text-muted">${formatDate(bonus.created_at)}</small>` : ''}
                        </div>
                    </div>
                `;
            });

            bonusList.html(bonusHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#total-bonus').text('₹0.00');
            $('#bonus-count').text('Error loading data');
            $('#claimed-count').text('0');
            $('#pending-count').text('0');
            $('#bonus-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadInviteBonusData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>