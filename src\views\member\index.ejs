<!DOCTYPE html>
<html translate="no" data-dpr="1" style="font-size: 48.16px;" style="min-height: 100vh;">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="robots" content="noindex,nofollow">
    <meta content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no" name="viewport">
    <title>Home</title>
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/all.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-thin.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-solid.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-regular.css">
    <link rel="stylesheet" href="https://site-assets.fontawesome.com/releases/v6.5.1/css/sharp-light.css">
    <link rel="stylesheet" href="./index_files/index-96409872.css">
    <link href="/css/member/main.css" rel="stylesheet" />
    <link href="/css/member/chunk-1.css" rel="stylesheet" />
    <link href="/css/member/chunk-2.css" rel="stylesheet" />
    <link href="/css/member/chunk-3.css" rel="stylesheet" />
    <link href="/css/member/chunk-4.css" rel="stylesheet" />
    <link href="/css/member/chunk-5.css" rel="stylesheet" />
    <link href="/css/member/chunk-6.css" rel="stylesheet" />
    <link href="/css/member/chunk-7.css" rel="stylesheet" />
    <link rel="shortcut icon" href="/images/bitbug_favicon.ico" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css" />
    <style>
        html,
        body {
            height: 100%;
            width: 100%;
            background-color: #090909;
            padding: 0;
            margin: 0;
        }

        .block-click {
            pointer-events: none;
        }

        .totalSavings {
            display: -webkit-box;
            display: -webkit-flex;
            display: flex;
            -webkit-box-orient: vertical;
            -webkit-box-direction: normal;
            -webkit-flex-direction: column;
            flex-direction: column;
            -webkit-box-align: center;
            -webkit-align-items: center;
            align-items: center
        }

        .totalSavingsIMG {
            width: 0.8rem
        }
    </style>
</head>

<body>
    <div id="app">
        <div data-v-8cd483ca="" class="mian page">
            <div data-v-106b99c8="" data-v-8cd483ca="" class="navbar">
                <div data-v-106b99c8="" class="navbar-left" onclick="location.href='/home'">
                    <div data-v-a9660e98="" data-v-106b99c8="" class="c-row c-row-middle-center">
                        <i class="fa-duotone fa-house fa-fade fa-lg center"
                            style="--fa-primary-color: #FFF; --fa-secondary-color: #FFF;"></i>
                    </div>
                </div>
                <div data-v-106b99c8="" class="navbar-right" onclick="location.href='/keFuMenu'">
                    <div data-v-8cd483ca="" data-v-106b99c8="" class="c-row">
                        <i class="fa-fade fa center">
                            <img data-v-8cd483ca="" data-v-106b99c8="" src="/images/audio.webp" class="audio">
                        </i>
                    </div>
                </div>
            </div>
            <div data-v-8cd483ca="" class="menu-box">
                <div data-v-8cd483ca="" class="info p-t-30 p-l-30 p-b-30 p-r-20" style="padding-top: 0rem!important">
                    <div data-v-8cd483ca="" class="c-row c-row-between c-row-middle state-box c-pr"
                        onclick="location.href='/myProfile'">
                        <div data-v-8cd483ca="" class="c-row c-row-middle">
                            <div data-v-8cd483ca="" class="user-img">
                                <img data-v-8cd483ca="" src="/images/avatar.svg" class="img">
                            </div>
                            <div data-v-8cd483ca="" class="p-l-10 infoName">
                                <div data-v-8cd483ca="" class="name mb3 c-row c-row-middle"> </div>
                                <div data-v-8cd483ca="" class="id tag-read mb3" data-clipboard-text="42472"></div>
                                <div data-v-8cd483ca="" class="number mb3"> </div>
                            </div>
                        </div>
                        <div data-v-8cd483ca="" class="profile">
                            <i data-v-8cd483ca="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                style="color: #8f5206; font-size: 20px;">
                            </i>
                        </div>
                    </div>
                </div>
                <div data-v-8cd483ca="" class="total-box">
                    <div data-v-8cd483ca="" class="infoItem">
                        <div data-v-8cd483ca="" class="c-row c-row-middle"
                            style="border-bottom: 0.01333rem solid #292929">
                            <div data-v-8cd483ca="" class="p-l-15">
                                <div data-v-8cd483ca="" class="des u-m-b-15">Balance</div>
                                <div data-v-8cd483ca="" class="c-row c-row-middle c-row-center p-t-5">
                                    <div data-v-8cd483ca="" class="money">
                                        <div data-v-8cd483ca="">
                                            <span data-v-8cd483ca="" class="txt1" id="Balance"
                                                style="display: inline; font-size: 22px"><i
                                                    class="fa-solid fa-asterisk fa-fade fa-sm center"
                                                    style="color: #D9AC4F;"></i> <i
                                                    class="fa-solid fa-asterisk fa-fade fa-sm center"
                                                    style="color: #D9AC4F;"></i> <i
                                                    class="fa-solid fa-asterisk fa-fade fa-sm center"
                                                    style="color: #D9AC4F;"></i> <i
                                                    class="fa-solid fa-asterisk fa-fade fa-sm center"
                                                    style="color: #D9AC4F;"></i> <i
                                                    class="fa-solid fa-asterisk fa-fade fa-sm center"
                                                    style="color: #D9AC4F;"></i></span>
                                            <span data-v-8cd483ca="" class="txt" id="balance_show"
                                                style="display: none">₹
                                                0.00</span>
                                        </div>
                                    </div>
                                    <div data-v-8cd483ca="">
                                        <img data-v-8cd483ca="" width="20px" height="20px" src="/images/king (1).png"
                                            class="img m-l-10 reload_money">
                                    </div>
                                </div>
                            </div>
                            <span toggle="#password-field"
                                class="fa-duotone fa-eye fa-fade fa-lg center field_icon toggle-password"
                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F; margin: 8px; align-self: end; margin-left: auto"
                                onclick="toggleBalanceVisibility()"></span>
                        </div>
                        <div data-v-8cd483ca="" class="c-row c-row-between m-t-10 infoBtn">
                            <div data-v-8cd483ca="" class="item c-row c-row-center" onclick="location.href='/wallet'">
                                <div data-v-8cd483ca="" class="totalSavings">
                                    <img data-v-d61ad6c9="" src="/images/walletIMG.png" class="totalSavingsIMG"><span
                                        data-v-d61ad6c9="">Wallet</span>
                                </div>
                            </div>
                            <div data-v-8cd483ca="" class="item c-row c-row-center"
                                onclick="location.href='/wallet/recharge'">
                                <div data-v-d61ad6c9="" class="totalSavings">
                                    <img data-v-d61ad6c9="" src="/images/recharge.png" class="totalSavingsIMG"><span
                                        data-v-d61ad6c9="">Deposit</span>
                                </div>
                            </div>
                            <div data-v-8cd483ca="" class="item c-row c-row-center"
                                onclick="location.href='/wallet/withdrawal'">
                                <div data-v-d61ad6c9="" class="totalSavings">
                                    <img data-v-d61ad6c9="" src="/images/withdraw.png" class="totalSavingsIMG"><span
                                        data-v-d61ad6c9="">Withdraw</span>
                                </div>
                            </div>
                            <div data-v-8cd483ca="" class="item c-row c-row-center"
                                onclick="location.href='/promotion'">
                                <div data-v-d61ad6c9="" class="totalSavings">
                                    <img data-v-d61ad6c9="" src="/images/promoteIMG.png" class="totalSavingsIMG"><span
                                        data-v-d61ad6c9="">Promotion</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div data-v-21f3500a="" data-v-8cd483ca="" class="list">
                    <% if (level==1) { %>
                        <div data-v-21f3500a="" class="item c-row c-row-between"
                            onclick="location.href='/admin/manager/index'">
                            <div data-v-21f3500a="" class="c-row c-row-middle">
                                <i class="fa-duotone fa-user-crown fa-fade fa-lg center"
                                    style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                <span data-v-21f3500a="" class="name">Administrator Area</span>
                            </div>
                            <div data-v-21f3500a="" class="c-row c-row-middle">
                                <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                    style="color: rgb(166, 169, 174); font-size: 20px;">
                                </i>
                            </div>
                        </div>
                        <% } %>
                            <% if (level==2) { %>
                                <div data-v-21f3500a="" class="item c-row c-row-between"
                                    onclick="location.href='/manager/index'">
                                    <div data-v-21f3500a="" class="c-row c-row-middle">
                                        <i class="fa-duotone fa-user-tie fa-fade fa-lg center"
                                            style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                        <span data-v-21f3500a="" class="name">Collaborator Area</span>
                                    </div>
                                    <div data-v-21f3500a="" class="c-row c-row-middle">
                                        <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                            style="color: rgb(166, 169, 174); font-size: 20px;">
                                        </i>
                                    </div>
                                </div>
                                <% } %>
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='/mian/forgot'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-key fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">Security & Safety</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle"><i data-v-21f3500a=""
                                                class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i></div>
                                    </div>
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='/redenvelopes'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-gift fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">Redeem Code</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle"><i data-v-21f3500a=""
                                                class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i></div>
                                    </div>
                                    <!--<div data-v-21f3500a="" class="item c-row c-row-between"
                                          onclick="location.href='#'">
                                        
                                         /newtutorial
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-circle-question fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">Guide for beginners</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle"><i data-v-21f3500a=""
                                                class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i></div>
                                    </div>-->
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='/about'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-circle-info fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">About Us</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i>
                                        </div>
                                    </div>
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='/recordsalary'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-badge-percent fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">Salary Record</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i>
                                        </div>
                                    </div>
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='<%=cskh%>'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fab fa-whatsapp fa-lg center" style="color: #D9AC4F"></i>
                                            <span data-v-21f3500a="" class="name">Whatsapp Support</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i>
                                        </div>
                                    </div>
                                    <div data-v-21f3500a="" class="item c-row c-row-between"
                                        onclick="location.href='/keFuMenu'">
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i class="fa-duotone fa-user-headset fa-fade fa-lg center"
                                                style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F;"></i>
                                            <span data-v-21f3500a="" class="name">Customer Support Online 24/7</span>
                                        </div>
                                        <div data-v-21f3500a="" class="c-row c-row-middle">
                                            <i data-v-21f3500a="" class="fa-regular fa-angle-right fa-fade fa-lg center"
                                                style="color: rgb(166, 169, 174); font-size: 20px;">
                                            </i>
                                        </div>
                                    </div>
                </div>
            </div>

            <div data-v-8cd483ca="" class="logout-btn m-t-40" style="margin-top: 0rem!important">
                <div data-v-8cd483ca="" class="gradient">
                    <button data-v-8cd483ca=""
                        class="logout van-button van-button--default van-button--normal van-button--block van-button--round"
                        style="display: -webkit-box;display: -webkit-flex;display: flex;-webkit-box-align: center;-webkit-align-items: center;align-items: center;-webkit-box-pack: center;-webkit-justify-content: center;justify-content: center;gap: 0.13333rem;width: 100%;height: 100%;padding-block: 0.13333rem;color: #d9ac4f;background: none;border: 0.01333rem solid #D9AC4F;border-radius: 0.53333rem;border-radius: 99rem;outline: none;font-size: .4rem;">
                        <i class="fa-duotone fa-power-off fa-fade fa-lg center"
                            style="--fa-primary-color: #D9AC4F; --fa-secondary-color: #D9AC4F; --fa-secondary-opacity: 0.5;"></i>
                        <div data-v-8cd483ca="" class="van-button__content">
                            <span data-v-8cd483ca="" class="van-button__text">
                                <span data-v-8cd483ca="">Log-Out</span>
                            </span>
                        </div>
                    </button>
                </div>
            </div>
            <div data-v-7692a079="" data-v-8cd483ca="" class="Loading c-row c-row-middle-center"
                style="/* display: none; */">
                <div data-v-7692a079="" class="van-loading van-loading--circular">
                    <span data-v-7692a079="" class="van-loading__spinner van-loading__spinner--circular"
                        style="width: auto; height: auto; -webkit-animation-duration: 1s; animation-duration: 1s;">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                            viewBox="0 0 200 200" width="200" height="200" preserveAspectRatio="xMidYMid meet"
                            style="width: 100%;height: 100%;transform: translate3d(0px, 0px, 0px);content-visibility: visible;">
                            <g transform="matrix(-0.5984085202217102,0.8011911511421204,-0.8011911511421204,-0.5984085202217102,239.9599609375,79.72173309326172)"
                                style="display: block;">
                                <image width="200px" height="200px" preserveAspectRatio="xMidYMid slice"
                                    xlink:href="/index_files/loadingspinner.png"></image>
                            </g>
                            <g transform="matrix(0.9408400058746338,0,0,0.9408400058746338,20.969436645507812,79.30152130126953)"
                                style="display: block;">
                                 
                            </g>
                        </svg>
                    </span>
                    <img src="/index_files/h5setting_202401100608011fs2.png" style="position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);width: 80%;height: 80%;margin-top: 10px;">
                </div>
            </div>
        </div>
    </div>
    <div class="van-overlay" style="z-index: 2001;display: none;"></div>
    <div role="dialog" class="van-dialog" style="z-index: 2002;display: none;" aria-labelledby="Lời nhắc">
        <div data-v-b9e16d43="" class="dialog__container-img"><img data-v-b9e16d43="" alt="" class=""
                data-origin="https://www.bigdaddygame2.com/assets/png/orderCancelWarn-93894f35.png"
                src="/assets/png/orderCancelWarn-93894f35.png" style="width: 2.13333rem;height: 2.13333rem"></div>
        <div class="van-dialog__content">
            <div class="van-dialog__message van-dialog__message--has-title dialog__container-title"
                style="font-size: .48rem;font-weight: 700;color: #fff">
                <h1>Do you want to log out?</h1>
            </div>
        </div>
        <div class="van--top van-dialog__footer van-dialog__footer--buttons">
            <button class="van-button van-button--default van-button--large van-dialog__cancel"
                style="width: 3.12rem;height: 1.06667rem;color: #d9ac4f;font-size: .42667rem;text-align: center;border-radius: 9rem;border: 0.01333rem solid #D9AC4F;background: transparent">
                <div class="van-button__content">
                    <span class="van-button__text">Cancel</span>
                </div>
            </button>
            <button class="van-button van-button--default van-button--large van-dialog__confirm van-hairline--left"
                style="width: 4.34667rem;height: 1.06667rem;font-size: .42667rem;text-align: center;border-radius: 9rem;border: 0.01333rem solid #D9AC4F;color: #8f5206;font-weight: 700;background: -webkit-linear-gradient(top,#FAE59F 0%,#C4933F 100%);background: linear-gradient(180deg,#FAE59F 0%,#C4933F 100%)">
                <div class="van-button__content">
                    <span class="van-button__text">Confirm</span>
                </div>
            </button>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        fetch('/api/webapi/GetUserInfo')
            .then(response => response.json())
            .then(data => {
                if (data.status === false) {
                    unsetCookie();
                    return false;
                };
                $('.infoName .name').text(data.data.name_user);
                $('.infoName .id').text(`ID:${data.data.id_user}`);
                $('.infoName .id').attr('data-clipboard-text', '');
                $('.infoName .number').text(`Mobile Number: +91 ${data.data.phone_user.slice(0, 2)}****${data.data.phone_user.slice(-4)}`);
                $('.money .txt').text(` ₹ ${data.data.money_user} `);
            });
    </script>
    <script>
        $('.reload_money').click(function (e) {
            e.preventDefault();
            $(this).addClass('action block-click');
            setTimeout(() => {
                $(this).removeClass('action block-click');
            }, 3000);
        });

        window.onload = function () {
            $('.Loading').fadeOut(10);
        }
        $('.van-dialog__cancel').click(function (e) {
            e.preventDefault();

            $('.van-dialog').addClass('van-dialog-bounce-leave-active van-dialog-bounce-leave-to');
            setTimeout(() => {
                $('.van-dialog').removeClass('van-dialog-bounce-leave-active van-dialog-bounce-leave-to');
                $('.van-dialog, .van-overlay').fadeOut(50);
            }, 200);
        });
        $('.logout').click(function (e) {
            e.preventDefault();
            $('.van-dialog, .van-overlay').css('display', '');
            $('.van-dialog').addClass('van-dialog-bounce-enter-active van-dialog-bounce-enter-to');
            setTimeout(() => {
                $('.van-dialog').removeClass('van-dialog-bounce-enter-active van-dialog-bounce-enter-to');
            }, 500);
        });
        function setCookie(cname, cvalue, exdays) {
            const d = new Date();
            d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
            let expires = "expires=" + d.toUTCString();
            document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
        }
        function getCookie(cname) {
            let name = cname + "=";
            let decodedCookie = decodeURIComponent(document.cookie);
            let ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return "";
        }

        function unsetCookie() {
            setCookie('token', '', 0);
            setCookie('auth', '', 0);
            var checkToken = getCookie('token');
            var checkAuth = getCookie('auth');
            if (!checkToken && !checkAuth) {
                location.href = "/login";
            } else {
                setCookie('token', '', 0);
                setCookie('auth', '', 0);
                location.href = "/login";
            }
        }

        $('.van-dialog__confirm').click(function (e) {
            e.preventDefault();
            unsetCookie();
        });

        $(document).on('click', '.toggle-password', function () {

            $(this).toggleClass("fa-eye-slash");

            var input = $("#pass_log_id");
            input.attr('type') === 'password' ? input.attr('type', 'text') : input.attr('type', 'password')
        });

        var isBalanceVisible = true;

        function toggleBalanceVisibility() {
            var actualBalance = document.getElementById("Balance");
            var hiddenBalance = document.getElementById("balance_show");

            isBalanceVisible = !isBalanceVisible;

            if (isBalanceVisible) {
                actualBalance.style.display = "inline";
                hiddenBalance.style.display = "none";
            } else {
                actualBalance.style.display = "none";
                hiddenBalance.style.display = "inline";
            }
        }

    </script>

    <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
</body>

</html>