<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referral Bonus - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Referral Bonus</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-gradient-success text-white border-0 rounded-3">
                            <div class="card-body text-center">
                                <h6 class="card-title mb-2">Total Referral Bonus</h6>
                                <h2 class="mb-0" id="total-bonus">₹0.00</h2>
                                <small id="bonus-count">0 referrals processed</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Referral Statistics -->
                <div class="row mb-3">
                    <div class="col-4">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body text-center py-2">
                                <i class="fa-solid fa-calendar-day text-primary mb-2" style="font-size: 20px;"></i>
                                <h6 class="card-title mb-1">Today</h6>
                                <h5 class="mb-0 text-primary" id="today-bonus">₹0.00</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body text-center py-2">
                                <i class="fa-solid fa-calendar-week text-warning mb-2" style="font-size: 20px;"></i>
                                <h6 class="card-title mb-1">This Week</h6>
                                <h5 class="mb-0 text-warning" id="week-bonus">₹0.00</h5>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card border-0 rounded-3 bg-light">
                            <div class="card-body text-center py-2">
                                <i class="fa-solid fa-calendar-alt text-success mb-2" style="font-size: 20px;"></i>
                                <h6 class="card-title mb-1">This Month</h6>
                                <h5 class="mb-0 text-success" id="month-bonus">₹0.00</h5>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Referral Bonus History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="bonus-list" class="list-group list-group-flush">
                                    <div class="list-group-item text-center py-4">
                                        <i class="fa-solid fa-users text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-2 mb-0 text-muted">No referral bonus data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Referral Bonus page loaded');
            loadReferralBonusData();
        });

        function loadReferralBonusData() {
            // Show loading state
            $('#total-bonus').text('Loading...');
            $('#bonus-list').html('<div class="list-group-item text-center py-4"><i class="fa-solid fa-spinner fa-spin text-muted" style="font-size: 48px;"></i><p class="mt-2 mb-0 text-muted">Loading referral bonus data...</p></div>');

            // Fetch referral bonus data
            $.ajax({
                type: "POST",
                url: "/api/webapi/referralBonus",
                data: {},
                dataType: "json",
                success: function(response) {
                    console.log('Referral bonus response:', response);
                    if (response.status === true) {
                        updateReferralBonusUI(response);
                    } else {
                        showError('Failed to load referral bonus data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading referral bonus data:', error);
                    showError('Error loading referral bonus data');
                }
            });
        }

        function updateReferralBonusUI(data) {
            const bonusRecords = data.record || [];
            let totalBonus = 0;
            let todayBonus = 0;
            let weekBonus = 0;
            let monthBonus = 0;

            const today = new Date();
            const startOfWeek = new Date(today.setDate(today.getDate() - today.getDay()));
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            // Calculate totals
            bonusRecords.forEach(bonus => {
                const amount = parseFloat(bonus.amount || 0);
                totalBonus += amount;

                if (bonus.date) {
                    const bonusDate = new Date(bonus.date);

                    // Today's bonus
                    if (bonusDate.toDateString() === new Date().toDateString()) {
                        todayBonus += amount;
                    }

                    // This week's bonus
                    if (bonusDate >= startOfWeek) {
                        weekBonus += amount;
                    }

                    // This month's bonus
                    if (bonusDate >= startOfMonth) {
                        monthBonus += amount;
                    }
                }
            });

            // Update summary cards
            $('#total-bonus').text(`₹${totalBonus.toFixed(2)}`);
            $('#bonus-count').text(`${bonusRecords.length} referrals processed`);
            $('#today-bonus').text(`₹${todayBonus.toFixed(2)}`);
            $('#week-bonus').text(`₹${weekBonus.toFixed(2)}`);
            $('#month-bonus').text(`₹${monthBonus.toFixed(2)}`);

            // Render bonus list
            renderReferralBonusList(bonusRecords);
        }

        function renderReferralBonusList(bonusRecords) {
            const bonusList = $('#bonus-list');

            if (bonusRecords.length === 0) {
                bonusList.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-users text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No referral bonus data available</p>
                    </div>
                `);
                return;
            }

            let bonusHTML = '';
            bonusRecords.forEach((bonus, index) => {
                        const amount = parseFloat(bonus.amount || 0);
                        const date = bonus.date ? formatDate(bonus.date) : 'N/A';

                        bonusHTML += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fa-solid fa-user-plus text-success"></i>
                            <div class="ms-3">
                                <h6 class="mb-0">Referral Bonus #${bonus.id || (index + 1)}</h6>
                                <small class="text-muted">Amount: ₹${amount.toFixed(2)}</small>
                                ${bonus.description ? `<br><small class="text-muted">${bonus.description}</small>` : ''}
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-success">Received</span>
                            <br><small class="text-muted">${date}</small>
                        </div>
                    </div>
                `;
            });

            bonusList.html(bonusHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#total-bonus').text('₹0.00');
            $('#bonus-count').text('Error loading data');
            $('#today-bonus').text('₹0.00');
            $('#week-bonus').text('₹0.00');
            $('#month-bonus').text('₹0.00');
            $('#bonus-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadReferralBonusData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>