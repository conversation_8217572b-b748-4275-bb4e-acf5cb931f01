<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deposit & Extra Bonus - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Deposit & Extra Bonus</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-6">
                        <div class="card bg-gradient-primary text-white border-0 rounded-3">
                            <div class="card-body text-center py-2">
                                <h6 class="card-title mb-1">Total Deposits</h6>
                                <h4 class="mb-0" id="total-deposits">₹0.00</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card bg-gradient-success text-white border-0 rounded-3">
                            <div class="card-body text-center py-2">
                                <h6 class="card-title mb-1">Extra Bonus</h6>
                                <h4 class="mb-0" id="extra-bonus">₹0.00</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Deposit Bonus History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="bonus-list" class="list-group list-group-flush">
                                    <div class="list-group-item text-center py-4">
                                        <i class="fa-solid fa-wallet text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-2 mb-0 text-muted">No deposit bonus data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Deposit & Extra Bonus page loaded');
            loadDepositBonusData();
        });

        function loadDepositBonusData() {
            // Show loading state
            $('#total-deposits').text('Loading...');
            $('#extra-bonus').text('Loading...');
            $('#bonus-list').html('<div class="list-group-item text-center py-4"><i class="fa-solid fa-spinner fa-spin text-muted" style="font-size: 48px;"></i><p class="mt-2 mb-0 text-muted">Loading deposit bonus data...</p></div>');

            // Fetch recharge bonus data
            $.ajax({
                type: "POST",
                url: "/api/webapi/rechargeBonus",
                data: {},
                dataType: "json",
                success: function(response) {
                    console.log('Recharge bonus response:', response);
                    if (response.status === true) {
                        updateDepositBonusUI(response);
                    } else {
                        showError('Failed to load deposit bonus data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading deposit bonus data:', error);
                    showError('Error loading deposit bonus data');
                }
            });
        }

        function updateDepositBonusUI(data) {
            const bonusRecords = data.record || [];
            let totalDeposits = 0;
            let extraBonus = 0;

            // Calculate totals
            bonusRecords.forEach(bonus => {
                const amount = parseFloat(bonus.amount || 0);
                totalDeposits += amount;
                extraBonus += amount * 0.1; // Assuming 10% extra bonus
            });

            // Update summary cards
            $('#total-deposits').text(`₹${totalDeposits.toFixed(2)}`);
            $('#extra-bonus').text(`₹${extraBonus.toFixed(2)}`);

            // Render bonus list
            renderDepositBonusList(bonusRecords);
        }

        function renderDepositBonusList(bonusRecords) {
            const bonusList = $('#bonus-list');

            if (bonusRecords.length === 0) {
                bonusList.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-credit-card text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No deposit bonus data available</p>
                    </div>
                `);
                return;
            }

            let bonusHTML = '';
            bonusRecords.forEach((bonus, index) => {
                const amount = parseFloat(bonus.amount || 0);
                const date = bonus.date ? formatDate(bonus.date) : 'N/A';

                bonusHTML += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fa-solid fa-credit-card text-info"></i>
                            <div class="ms-3">
                                <h6 class="mb-0">Deposit Bonus #${bonus.id || (index + 1)}</h6>
                                <small class="text-muted">Amount: ₹${amount.toFixed(2)}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-info">Processed</span>
                            <br><small class="text-muted">${date}</small>
                        </div>
                    </div>
                `;
            });

            bonusList.html(bonusHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#total-deposits').text('₹0.00');
            $('#extra-bonus').text('₹0.00');
            $('#bonus-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadDepositBonusData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>