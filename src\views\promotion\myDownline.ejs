<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Downline - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">My Downline</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-4">
                        <div class="card bg-gradient-primary text-white border-0 rounded-3">
                            <div class="card-body text-center py-2" style="
    background: linear-gradient(90deg, #E67302 0%, #ee0a24 100%);
">
                                <h6 class="card-title mb-1">Level 1</h6>
                                <h4 class="mb-0" id="level1-count">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="card bg-gradient-success text-white border-0 rounded-3">
                            <div class="card-body text-center py-2" style="
    background: linear-gradient(90deg, #E67302 0%, #ee0a24 100%);">
                                <h6 class=" card-title mb-1 ">Level 2</h6>
                                <h4 class="mb-0 " id="level2-count ">0</h4>
                            </div>
                        </div>
                    </div>
                    <div class="col-4 ">
                        <div class="card bg-gradient-warning text-white border-0 rounded-3 ">
                            <div class="card-body text-center py-2 " style="
    background: linear-gradient(90deg, #E67302 0%, #ee0a24 100%);">
                                <h6 class=" card-title mb-1 ">Level 3</h6>
                                <h4 class="mb-0 " id="level3-count ">0</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row ">
                    <div class="col-12 ">
                        <div class="card border-0 rounded-3 ">
                            <div class="card-header bg-theme1 text-white ">
                                <h6 class="mb-0 ">Downline Structure</h6>
                            </div>
                            <div class="card-body p-0 ">
                                <div id="downline-list " class="list-group list-group-flush ">
                                    <div class="list-group-item text-center py-4 ">
                                        <i class="fa-solid fa-sitemap text-muted " style="font-size: 48px; "></i>
                                        <p class="mt-2 mb-0 text-muted ">No downline data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js "></script>
    <script src="/js/client.js "></script>
    <script>
        $(document).ready(function() {
            console.log('My Downline page loaded');
            loadDownlineData();
        });

        function loadDownlineData() {
            // Show loading state
            $('#level1-count').text('Loading...');
            $('#level2-count').text('Loading...');
            $('#level3-count').text('Loading...');
            $('#downline-list').html('<div class="list-group-item text-center py-4 "><i class="fa-solid fa-spinner fa-spin text-muted " style="font-size: 48px; "></i><p class="mt-2 mb-0 text-muted ">Loading downline data...</p></div>');

            // Fetch team data for downline structure
            $.ajax({
                type: "GET",
                url: "/api/webapi/myTeam",
                dataType: "json",
                success: function(response) {
                    console.log('Team response:', response);
                    if (response.status === true) {
                        updateDownlineUI(response);
                    } else {
                        showError('Failed to load downline data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading downline data:', error);
                    showError('Error loading downline data');
                }
            });
        }

        function updateDownlineUI(data) {
            const teamMembers = data.mem || [];
            const f1Members = data.f1 || [];
            const f1DirectMembers = data.f1_direct || [];

            console.log('Team data breakdown:', {
                f1Count: f1Members.length,
                f1DirectCount: f1DirectMembers.length,
                totalMembers: teamMembers.length
            });

            // Calculate level counts based on actual data
            const level1Count = f1Members.length;
            const level2Count = f1DirectMembers.length;
            const level3Count = Math.max(0, teamMembers.length - f1Members.length - f1DirectMembers.length);

            // Update level counts (fix selector issues)
            $('#level1-count').text(level1Count);
            $('#level2-count').text(level2Count);
            $('#level3-count').text(level3Count);

            // Render downline structure
            renderDownlineStructure(f1Members, f1DirectMembers, teamMembers);
        }

        function renderDownlineStructure(f1Members, f1DirectMembers, teamMembers) {
            const downlineList = $('#downline-list');

            if (f1Members.length === 0) {
                downlineList.html(`
                    <div class="list-group-item text-center py-4 ">
                        <i class="fa-solid fa-sitemap text-muted " style="font-size: 48px; "></i>
                        <p class="mt-2 mb-0 text-muted ">No downline data available</p>
                    </div>
                `);
                return;
            }

            let downlineHTML = '';

            // Display Level 1 members
            f1Members.forEach((member, index) => {
                const joinDate = member.time ? formatDate(member.time) : 'N/A';
                const memberPhone = member.phone ?
                    `${member.phone.slice(0, 2)}****${member.phone.slice(-4)}` :
                    'Unknown';

                const statusBadge = member.status === 1 ?
                    '<span class="badge bg-success ">Active</span>' :
                    '<span class="badge bg-secondary ">Inactive</span>';

                downlineHTML += `
                    <div class="list-group-item ">
                        <div class="d-flex justify-content-between align-items-center ">
                            <div class="d-flex align-items-center ">
                                <div class="level-indicator bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3 " style="width: 30px; height: 30px; font-size: 12px; ">L1</div>
                                <div>
                                    <h6 class="mb-0 ">${member.name_user || 'Member'}</h6>
                                    <small class="text-muted ">ID: ${member.id_user || 'N/A'}</small>
                                    <br><small class="text-muted ">Phone: ${memberPhone}</small>
                                </div>
                            </div>
                            <div class="text-end ">
                                ${statusBadge}
                                <br><small class="text-muted ">${joinDate}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Display Level 2 members (F1 Direct)
            f1DirectMembers.forEach((member, index) => {
                const joinDate = member.time ? formatDate(member.time) : 'N/A';
                const memberPhone = member.phone ?
                    `${member.phone.slice(0, 2)}****${member.phone.slice(-4)}` :
                    'Unknown';

                const statusBadge = member.status === 1 ?
                    '<span class="badge bg-success">Active</span>' :
                    '<span class="badge bg-secondary">Inactive</span>';

                downlineHTML += `
                    <div class="list-group-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <div class="level-indicator bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 30px; height: 30px; font-size: 12px;">L2</div>
                                <div>
                                    <h6 class="mb-0">${member.name_user || 'Level 2 Member'}</h6>
                                    <small class="text-muted">ID: ${member.id_user || 'N/A'}</small>
                                    <br><small class="text-muted">Phone: ${memberPhone}</small>
                                    <br><small class="text-muted">Code: ${member.code || 'N/A'}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                ${statusBadge}
                                <br><small class="text-muted">${joinDate}</small>
                            </div>
                        </div>
                    </div>
                `;
            });

            // Add some Level 3 examples if team members exist beyond F1 and F1_direct
            if (teamMembers.length > (f1Members.length + f1DirectMembers.length)) {
                const remainingMembers = teamMembers.slice(f1Members.length);

                // Add a few Level 2 examples
                remainingMembers.slice(0, 3).forEach((member, index) => {
                    const joinDate = member.time ? formatDate(member.time) : 'N/A';
                    const memberPhone = member.phone ?
                        `${member.phone.slice(0, 2)}****${member.phone.slice(-4)}` :
                        'Unknown';

                    downlineHTML += `
                        <div class="list-group-item ">
                            <div class="d-flex justify-content-between align-items-center ">
                                <div class="d-flex align-items-center ">
                                    <div class="level-indicator bg-success text-white rounded-circle d-flex align-items-center justify-content-center me-3 " style="width: 30px; height: 30px; font-size: 12px; ">L2</div>
                                    <div>
                                        <h6 class="mb-0 ">Level 2 Member</h6>
                                        <small class="text-muted ">ID: ${member.id_user || 'N/A'}</small>
                                        <br><small class="text-muted ">Phone: ${memberPhone}</small>
                                    </div>
                                </div>
                                <div class="text-end ">
                                    <span class="badge bg-success ">Active</span>
                                    <br><small class="text-muted ">${joinDate}</small>
                                </div>
                            </div>
                        </div>
                    `;
                });

                // Add a Level 3 example
                if (remainingMembers.length > 3) {
                    const member = remainingMembers[3];
                    const joinDate = member.time ? formatDate(member.time) : 'N/A';
                    const memberPhone = member.phone ?
                        `${member.phone.slice(0, 2)}****${member.phone.slice(-4)}` :
                        'Unknown';

                    downlineHTML += `
                        <div class="list-group-item ">
                            <div class="d-flex justify-content-between align-items-center ">
                                <div class="d-flex align-items-center ">
                                    <div class="level-indicator bg-warning text-white rounded-circle d-flex align-items-center justify-content-center me-3 " style="width: 30px; height: 30px; font-size: 12px; ">L3</div>
                                    <div>
                                        <h6 class="mb-0 ">Level 3 Member</h6>
                                        <small class="text-muted ">ID: ${member.id_user || 'N/A'}</small>
                                        <br><small class="text-muted ">Phone: ${memberPhone}</small>
                                    </div>
                                </div>
                                <div class="text-end ">
                                    <span class="badge bg-success ">Active</span>
                                    <br><small class="text-muted ">${joinDate}</small>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }

            downlineList.html(downlineHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#level1-count').text('0');
            $('#level2-count').text('0');
            $('#level3-count').text('0');
            $('#downline-list').html(`
                <div class="list-group-item text-center py-4 ">
                    <i class="fa-solid fa-exclamation-triangle text-danger " style="font-size: 48px; "></i>
                    <p class="mt-2 mb-0 text-danger ">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2 " onclick="loadDownlineData() ">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>