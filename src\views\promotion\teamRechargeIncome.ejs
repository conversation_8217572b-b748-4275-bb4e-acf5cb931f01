<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Level UP Reward - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Level UP Reward</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-gradient-warning text-white border-0 rounded-3">
                            <div class="card-body text-center" style="
    background: linear-gradient(90deg, #E67302 0%, #ee0a24 100%);
">
                                <h6 class="card-title mb-2">Total Level UP Rewards</h6>
                                <h2 class="mb-0" id="total-rewards">₹0.00</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Reward History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="reward-list" class="list-group list-group-flush">
                                    <div class="list-group-item text-center py-4">
                                        <i class="fa-solid fa-trophy text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-2 mb-0 text-muted">No level up reward data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Team Recharge Income page loaded');
            loadTeamRechargeData();
        });

        function loadTeamRechargeData() {
            // Show loading state
            $('#total-rewards').text('Loading...');
            $('#reward-list').html('<div class="list-group-item text-center py-4"><i class="fa-solid fa-spinner fa-spin text-muted" style="font-size: 48px;"></i><p class="mt-2 mb-0 text-muted">Loading team recharge data...</p></div>');

            // Fetch team data and recharge bonus data
            Promise.all([
                $.ajax({
                    type: "GET",
                    url: "/api/webapi/myTeam",
                    dataType: "json"
                }),
                $.ajax({
                    type: "POST",
                    url: "/api/webapi/rechargeBonus",
                    data: {},
                    dataType: "json"
                })
            ]).then(function([teamResponse, rechargeResponse]) {
                console.log('Team response:', teamResponse);
                console.log('Recharge response:', rechargeResponse);

                if (teamResponse.status === true && rechargeResponse.status === true) {
                    updateTeamRechargeUI(teamResponse, rechargeResponse);
                } else {
                    showError('Failed to load team recharge data');
                }
            }).catch(function(error) {
                console.error('Error loading team recharge data:', error);
                showError('Error loading team recharge data');
            });
        }

        function updateTeamRechargeUI(teamData, rechargeData) {
            const teamMembers = teamData.mem || [];
            const rechargeRecords = rechargeData.record || [];

            let totalRewards = 0;
            let teamRechargeCount = 0;

            // Calculate team recharge rewards
            rechargeRecords.forEach(recharge => {
                const amount = parseFloat(recharge.amount || 0);
                // Assuming 2% team recharge commission
                const teamReward = amount * 0.02;
                totalRewards += teamReward;
                teamRechargeCount++;
            });

            // Update summary cards
            $('#total-rewards').text(`₹${totalRewards.toFixed(2)}`);

            // Render team recharge list
            renderTeamRechargeList(rechargeRecords, teamMembers);
        }

        function renderTeamRechargeList(rechargeRecords, teamMembers) {
            const rewardList = $('#reward-list');

            if (rechargeRecords.length === 0) {
                rewardList.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-users text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No team recharge data available</p>
                    </div>
                `);
                return;
            }

            let rewardHTML = '';
            rechargeRecords.forEach((recharge, index) => {
                const amount = parseFloat(recharge.amount || 0);
                const teamReward = amount * 0.02; // 2% team commission
                const date = recharge.date ? formatDate(recharge.date) : 'N/A';

                // Find team member info if available
                const memberInfo = teamMembers.find(member => member.phone === recharge.phone) || {};
                const memberDisplay = memberInfo.phone ?
                    `${memberInfo.phone.slice(0, 2)}****${memberInfo.phone.slice(-4)}` :
                    'Team Member';

                rewardHTML += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fa-solid fa-credit-card text-warning"></i>
                            <div class="ms-3">
                                <h6 class="mb-0">Team Recharge Reward #${recharge.id || (index + 1)}</h6>
                                <small class="text-muted">Member: ${memberDisplay}</small>
                                <br><small class="text-muted">Recharge: ₹${amount.toFixed(2)} → Reward: ₹${teamReward.toFixed(2)}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-warning">Earned</span>
                            <br><small class="text-muted">${date}</small>
                        </div>
                    </div>
                `;
            });

            rewardList.html(rewardHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#total-rewards').text('₹0.00');
            $('#reward-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadTeamRechargeData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>