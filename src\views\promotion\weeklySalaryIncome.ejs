<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link rel="icon" type="image/svg+xml" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Self Trade Rebate Income - DeltInWin</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" crossorigin="" href="/index_files/index-BUBUniRp.css">
</head>

<body style="font-size: 12px;">
    <div id="root">
        <div class="mainApp">
            <div class="container-fluid" style="padding: 4px 12px;">
                <div class="row align-items-center mb-3">
                    <div class="col-2">
                        <a href="/promotion" class="text-decoration-none">
                            <i class="fa-solid fa-arrow-left text-theme1" style="font-size: 20px;"></i>
                        </a>
                    </div>
                    <div class="col-8 text-center">
                        <h5 class="mb-0 text-theme1 fw-bold">Self Trade Rebate Income</h5>
                    </div>
                    <div class="col-2"></div>
                </div>

                <div class="row mb-3">
                    <div class="col-12">
                        <div class="card bg-gradient-info text-white border-0 rounded-3">
                            <div class="card-body text-center">
                                <h6 class="card-title mb-2">Total Rebate Income</h6>
                                <h2 class="mb-0" id="total-rebate">₹0.00</h2>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <div class="card border-0 rounded-3">
                            <div class="card-header bg-theme1 text-white">
                                <h6 class="mb-0">Rebate History</h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="rebate-list" class="list-group list-group-flush">
                                    <div class="list-group-item text-center py-4">
                                        <i class="fa-solid fa-chart-line text-muted" style="font-size: 48px;"></i>
                                        <p class="mt-2 mb-0 text-muted">No rebate data available</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="/js/client.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Weekly Salary Income page loaded');
            loadWeeklySalaryData();
        });

        function loadWeeklySalaryData() {
            // Show loading state
            $('#total-rebate').text('Loading...');
            $('#rebate-list').html('<div class="list-group-item text-center py-4"><i class="fa-solid fa-spinner fa-spin text-muted" style="font-size: 48px;"></i><p class="mt-2 mb-0 text-muted">Loading weekly salary data...</p></div>');

            // Fetch weekly salary data
            $.ajax({
                type: "POST",
                url: "/api/webapi/WeeklySalary",
                data: {},
                dataType: "json",
                success: function(response) {
                    console.log('Weekly salary response:', response);
                    if (response.status === true) {
                        updateWeeklySalaryUI(response);
                    } else {
                        showError('Failed to load weekly salary data');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading weekly salary data:', error);
                    showError('Error loading weekly salary data');
                }
            });
        }

        function updateWeeklySalaryUI(data) {
            const salaryRecords = data.record || [];
            let totalSalary = 0;

            // Calculate totals
            salaryRecords.forEach(salary => {
                const amount = parseFloat(salary.amount || 0);
                totalSalary += amount;
            });

            // Update summary cards
            $('#total-rebate').text(`₹${totalSalary.toFixed(2)}`);

            // Render salary list
            renderWeeklySalaryList(salaryRecords);
        }

        function renderWeeklySalaryList(salaryRecords) {
            const salaryList = $('#rebate-list');

            if (salaryRecords.length === 0) {
                salaryList.html(`
                    <div class="list-group-item text-center py-4">
                        <i class="fa-solid fa-calendar-week text-muted" style="font-size: 48px;"></i>
                        <p class="mt-2 mb-0 text-muted">No weekly salary data available</p>
                    </div>
                `);
                return;
            }

            let salaryHTML = '';
            salaryRecords.forEach((salary, index) => {
                const amount = parseFloat(salary.amount || 0);
                const date = salary.date ? formatDate(salary.date) : 'N/A';

                salaryHTML += `
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fa-solid fa-calendar-week text-info"></i>
                            <div class="ms-3">
                                <h6 class="mb-0">Weekly Salary #${salary.id || (index + 1)}</h6>
                                <small class="text-muted">Amount: ₹${amount.toFixed(2)}</small>
                            </div>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-info">Paid</span>
                            <br><small class="text-muted">${date}</small>
                        </div>
                    </div>
                `;
            });

            salaryList.html(salaryHTML);
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-IN', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        function showError(message) {
            $('#total-rebate').text('₹0.00');
            $('#rebate-list').html(`
                <div class="list-group-item text-center py-4">
                    <i class="fa-solid fa-exclamation-triangle text-danger" style="font-size: 48px;"></i>
                    <p class="mt-2 mb-0 text-danger">${message}</p>
                    <button class="btn btn-sm btn-outline-primary mt-2" onclick="loadWeeklySalaryData()">Retry</button>
                </div>
            `);
        }
    </script>
</body>

</html>